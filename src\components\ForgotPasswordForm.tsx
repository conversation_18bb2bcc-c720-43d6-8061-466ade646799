import React from 'react';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { ChevronLeft } from 'lucide-react';

interface ForgotPasswordFormProps {
  onForgotPassword: (username: string) => Promise<void>;
  onSwitchTab: () => void;
  error: string;
}

const ForgotPasswordSchema = Yup.object().shape({
  username: Yup.string()
    .trim()
    .min(3, 'Username must be at least 3 characters')
    .required('Username is required'),
});

const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  onForgotPassword,
  onSwitchTab,
  error,
}) => {
  const handleSubmit = (
    values: { username: string },
    { setSubmitting }: FormikHelpers<{ username: string }>
  ) => {
    onForgotPassword(values.username);
    setSubmitting(false);
  };

  return (
    <div className="max-w-sm mx-auto">
      <div className="flex items-center gap-1.3 mb-4">
        <button
          type="button"
          onClick={() => {
            console.log('Back clicked');
            onSwitchTab();
          }}
          className="text-white"
        >
          <ChevronLeft className='font-bold' />
        </button>
        <h2 className="text-[16px] leading-[24px] font-bold tracking-[0.02rem] uppercase font-[Oswald] text-white">
          FORGOT PASSWORD
        </h2>

      </div>

      <Formik
        initialValues={{ username: '' }}
        validationSchema={ForgotPasswordSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, errors, touched }) => (
          <Form>
            <label htmlFor="username" className="block text-sm font-normal font-[Poppins] mb-1">
              Username or Email
            </label>
            <Field
              id="username"
              name="username"
              placeholder="Enter your username or email"
              className={`w-full bg-white/10 border ${errors.username && touched.username
                  ? 'border-red-500'
                  : 'border-white/20'
                } rounded-lg pl-2  py-2 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400`}
            />
            <ErrorMessage
              name="username"
              component="div"
              className="text-red-500 mb-2 mt-2 "
            />

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full p-2 mt-6 rounded font-normal flex text-white rounded-lg  tracking-wider
                   uppercase font-[Anton] justify-center items-center bg-[#ED0CFF]"
            >
              {isSubmitting ? 'Sending OTP...' : 'Send OTP'}
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default ForgotPasswordForm;
