import React, { useState, useEffect, useRef } from 'react';
import { Gift, CheckCircle } from 'lucide-react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useAuth } from '../auth/AuthContext';
import {
  getScratchCardPrice,
  placeScratchCardBet,
} from '../api/scratchCardService';
import { useCoins } from '../hooks/useCoinsQuery';
import { CustomToast } from '../utils/validations/customeToast';
import winSound from '../Sounds/Scratch/scratch_win.mp3';
import BackGroundSound from '../Sounds/Scratch/Scratch_Scratch_Sound.mp3';
import SoundManager from '../components/soundManager/SoundManager';
import { IMAGES } from '../constant/image';
import { AnimatedCard } from './AnimatedCard/AnimatedCard';
import { preloadImage } from '../utils/helper';
import { PzPopup } from './Shared/PzPopup';
import { PzButton } from './Shared/PzButton';
import { useGameDataLoading } from '../hooks/useGameDataLoading';
import { GameLoadingScreen } from './GameLoadingScreen/GameLoadingScreen';
interface ScratchCardProps {
  onClose: () => void;
  onWin?: (reward: { type: string; amount: number }) => void;
  embedded?: boolean;
  balance?: number;
  onPurchase?: (cost: number) => void;
}

type CardIndex = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9';

// Map API symbols to icons
const SYMBOL_MAP = {
  scratch_cent: {
    icon: IMAGES.DOLLAR_100_FRAMES,
    color: '#f97316',
    label: 'Cent',
  },
  scratch_dollar: {
    icon: IMAGES.DOLLAR_FRAMES,
    color: '#fbbf24',
    label: 'Dollar',
  },
  scratch_cup: {
    icon: IMAGES.TOKEN_100_FRAMES,
    color: '#3b82f6',
    label: 'Cup',
  },
  scratch_car: { icon: IMAGES.TOKEN_FRAMES, color: '#ec4899', label: 'Car' },
  scratch_diamond: {
    icon: IMAGES.ARTIME_FRAMES,
    color: '#8b5cf6',
    label: 'Diamond',
  },
  scratch_crawn: { icon: IMAGES.CAP_FRAMES, color: '#10b981', label: 'Crown' },
};

const CARD_SCRATCH_TIME = 1000;

const ScratchCard: React.FC<ScratchCardProps> = ({
  onWin,
  onPurchase,
  onClose,
}) => {
  const { accessToken } = useAuth();
  const { coins } = useCoins();

  const [cost, setCost] = useState<number>(0);
  const [isPurchased, setIsPurchased] = useState(false);
  const [showPurchaseError, setShowPurchaseError] = useState(false);
  const [hasRevealedAll, setHasRevealedAll] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [reward, setReward] = useState<{ type: string; amount: number } | null>(
    null
  );
  const [board, setBoard] = useState<string[][]>([]);
  const [winningSymbol, setWinningSymbol] = useState<string>('');
  const [matchCells, setMatchCells] = useState<number[][] | null>(null);
  const [prize, setPrize] = useState<string>('0');
  const containerRef = useRef<HTMLDivElement>(null);
  const [revealCard, setRevealCard] = useState(false);
  const [isPlayingAgain, setIsPlayingAgain] = useState(false);
  const [didWin, setDidWin] = useState(false);
  const [animatedCardIndexes, setAnimatedCardIndexes] = useState<number[]>([]);

  const {
    data: scratchCardPrice,
    refetch: refetchPrice,
    isLoading: isPriceLoading,
    isError: isPriceError,
  } = useQuery({
    queryKey: ['scratchCardPrice'],
    queryFn: () => {
      if (!accessToken) throw new Error('Access token missing');
      return getScratchCardPrice(accessToken);
    },
    refetchOnMount: true,
    enabled: !!accessToken,
  });

  const placeBetMutation = useMutation({
    mutationFn: async () => {
      if (!accessToken) throw new Error('Access token missing');
      const result = await placeScratchCardBet(accessToken);
      onPurchase?.(cost);

      console.log('🚀 bet result:', result);
      return result;
    },
    onSuccess: (data) => {
      setBoard(data.board);
      setWinningSymbol(data.winning_symbol);
      setMatchCells(data.match_cells);
      setPrize(data.prize);
      setIsPurchased(true);
      if (data.prize !== '0' && parseInt(data.prize) > 0) {
        const prizeAmount = parseInt(data.prize);
        setReward({ type: 'coins', amount: prizeAmount });
      }
      CustomToast('success', `Card purchased for ${data.bet_amount} bucks`);
    },
    onError: () => {
      CustomToast('error', 'Failed to place bet.');
    },
  });

  useEffect(() => {
    // Preload all animated images to speed up their appearance
    [
      IMAGES.SCRATCH_GAME_WELCOME,
      IMAGES.CAP_FRAMES,
      IMAGES.TOKEN_FRAMES,
      IMAGES.ARTIME_FRAMES,
      IMAGES.DOLLAR_FRAMES,
      IMAGES.TOKEN_100_FRAMES,
      IMAGES.DOLLAR_100_FRAMES,
    ].forEach(preloadImage);
  }, []);

  useEffect(() => {
    if (scratchCardPrice?.price) {
      setCost(scratchCardPrice.price);
    }
  }, [scratchCardPrice]);

  useEffect(() => {
    isPriceError && CustomToast('error', 'Failed to get price');
  }, [isPriceError]);

  // Modified to show confirmation dialog first
  const handlePurchaseClick = () => {
    if (coins >= cost) {
      handleConfirmPurchase();
    } else {
      setShowPurchaseError(true);
      setTimeout(() => setShowPurchaseError(false), 3000);
    }
  };

  // Actual purchase function after confirmation
  const handleConfirmPurchase = () => {
    // Reset all game states before starting a new game
    setHasRevealedAll(false);
    setShowResult(false);
    setReward(null);
    setBoard([]);
    setWinningSymbol('');
    setMatchCells(null);
    setPrize('0');
    setRevealCard(false);
    setAnimatedCardIndexes([]);

    // Place the bet - ensure board is updated which will trigger the effect to redraw canvases
    placeBetMutation.mutate();
  };

  const handleRevealAll = () => {
    if (!isPurchased || hasRevealedAll) return;

    setRevealCard(true);
    setAnimatedCardIndexes(Array.from({ length: 9 }, (_, index) => index));

    setTimeout(() => {
      setHasRevealedAll(true);
      setShowResult(true);
      setTimeout(() => {
        setRevealCard(false);
        if (reward && !showResult) {
          onWin?.(reward);
          setDidWin(true);
        }
      }, CARD_SCRATCH_TIME);
    });
  };

  // New function to handle play again - directly show purchase confirmation
  const handlePlayAgain = () => {
    handleConfirmPurchase();
    setIsPlayingAgain(true);

    // Re-fetch price in case it changed
    refetchPrice().then(() => {
      // Reset playing again flag
      setIsPlayingAgain(false);

      // Show confirmation directly instead of going back to purchase UI
      if (coins < cost) {
        setShowPurchaseError(true);
        // Reset states to show purchase UI since they don't have enough coins
        setIsPurchased(false);
        setHasRevealedAll(false);
        setShowResult(false);
        setReward(null);
        setBoard([]);
        setWinningSymbol('');
        setMatchCells(null);
        setPrize('0');
        setRevealCard(false);
        setTimeout(() => setShowPurchaseError(false), 3000);
      }
    });
  };

  // Convert board to flat array for rendering
  const flatBoard = board.length > 0 ? board.flat() : Array(9).fill(null);

  // Helper to check if a cell is part of the winning combination
  const isWinningCell = (index: number): boolean => {
    if (!matchCells) {
      // If matchCells is not provided, check if this cell has the winning symbol
      if (winningSymbol && flatBoard[index] === winningSymbol) {
        return true;
      }
      return false;
    }

    const row = Math.floor(index / 3);
    const col = index % 3;

    return matchCells.some((cell) => cell[0] === row && cell[1] === col);
  };

  const checkGameProgress = () => {
    // If more than half the tiles are revealed or if there's a win, show result
    if (animatedCardIndexes.length > 3 && !showResult) {
      handleRevealAll();
      if (reward && !showResult) {
        onWin?.(reward);
      }
    }
  };

  const handleStartScratch = (index: number) => {
    setAnimatedCardIndexes((prevIndexes) => [...prevIndexes, index]);
    setTimeout(() => setRevealCard(false), CARD_SCRATCH_TIME);

    setRevealCard(true);
    checkGameProgress();
  };
  const handleEndScratch = () => setRevealCard(false);

  const { isGameDataLoading } = useGameDataLoading(
    !isPriceLoading,
    Number.isFinite(coins)
  );

  return isGameDataLoading ? (
    <GameLoadingScreen isOuter />
  ) : (
    <PzPopup title="Scratch Card" onClose={onClose}>
      <SoundManager
        sounds={{
          background: BackGroundSound,
          win: winSound,
        }}
        // loop the background while the game is running:
        backgroundKey={revealCard ? 'background' : null}
        // play one of these exactly once when it changes:
        playKey={didWin ? 'win' : undefined}
        volumes={{
          background: 0.4,
          win: 1.0,
        }}
      />

      {!isPurchased ? (
        <div className="text-center">
          <img
            src={IMAGES.SCRATCH_GAME_WELCOME}
            alt="Diamond gift"
            style={{
              width: 130,
              height: 120,
              transform: 'rotate(-3.5deg)',
              margin: '2rem auto',
            }}
          />
          <h3
            className="text-4xl mb-2"
            style={{
              fontFamily: 'Bebas Neue',
            }}
          >
            TRY YOUR LUCK!
          </h3>
          <p className="font-[Poppins] text-xs text-white/60 mb-8">
            Purchase a scratch card for{' '}
            <span className="font-bold text-white">{cost}</span> bucks
            <br />
            Win up to <span className="font-bold text-white">5000</span> bucks!
          </p>
          <PzButton
            text={
              placeBetMutation.isPending
                ? 'Processing...'
                : isPlayingAgain
                ? 'Loading...'
                : 'Purchase Card'
            }
            isDisabled={
              isPriceLoading || placeBetMutation.isPending || isPlayingAgain
            }
            onClick={handlePurchaseClick}
          />
          <div className="text-sm m-auto text-center mt-2 font-[Anton]">
            Purchase a scratch card for {cost} bucks
          </div>
          {showPurchaseError && (
            <p className="text-red-400 mt-4">
              Insufficient bucks. You need {cost} bucks to purchase.
            </p>
          )}
        </div>
      ) : (
        <>
          <div ref={containerRef} className="grid grid-cols-3 gap-2 mt-8 mb-8">
            {flatBoard.map((symbol, index) => {
              // Get the icon component for this symbol
              const symbolInfo = SYMBOL_MAP[
                symbol as keyof typeof SYMBOL_MAP
              ] || {
                icon: Gift,
                color: '#64748b',
              };

              const cardIndex = String(index + 1) as CardIndex;
              const isWinning = isWinningCell(index);
              const showAnimatedCard = animatedCardIndexes.includes(index);

              return (
                <div key={index} className={`aspect-square relative`}>
                  {/* Icon Container */}
                  {showAnimatedCard ? (
                    <AnimatedCard
                      image={symbolInfo.icon}
                      {...(isWinning &&
                        hasRevealedAll && {
                          className:
                            'border-2 border-solid border-yellow-400 rounded-3xl',
                        })}
                    />
                  ) : (
                    <div
                      style={{
                        width: 106,
                        height: 106,
                        backgroundImage: `url(${
                          IMAGES[`SCRATCH_CARD_${cardIndex}`]
                        })`,
                        backgroundSize: 'cover',
                        borderRadius: '1.5rem',
                      }}
                      className="absolute inset-0 w-full h-full cursor-pointer touch-none z-20"
                      onMouseDown={() => handleStartScratch(index)}
                      onMouseUp={handleEndScratch}
                      onTouchStart={() => handleStartScratch(index)}
                      onTouchEnd={handleEndScratch}
                    />
                  )}
                </div>
              );
            })}
          </div>

          {showResult && reward && parseInt(prize) > 0 && (
            <div className="text-center mb-4 animate-bounce">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <CheckCircle size={32} className="text-green-400" />
              </div>
              <p className="text-xl font-bold text-green-400">
                You won {prize} bucks!
              </p>
            </div>
          )}

          {showResult && (!reward || parseInt(prize) <= 0) && (
            <div className="text-center mb-4 animate-fade-in">
              <p className="text-lg font-medium text-white/80">
                No win this time. Try again!
              </p>
            </div>
          )}

          {/* Conditional rendering for Reveal All or Play Again button */}
          {hasRevealedAll ? (
            <PzButton text="Play Again" onClick={handlePlayAgain} />
          ) : (
            <PzButton text="Reveal All" onClick={handleRevealAll} />
          )}
        </>
      )}
    </PzPopup>
  );
};

export default ScratchCard;
