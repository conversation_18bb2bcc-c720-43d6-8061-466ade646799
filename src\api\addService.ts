
declare global {
  interface Window {
    AndroidBridge?: {
      postMessage: (message: string) => void;
    };
    onNativeMessage?: (payload: NativeMessagePayload) => void;
  }
}

export interface NativeMessagePayload<T = unknown> {
  action: string;
  data?: T;
}

type RewardDetails = {
  userId: string;
};

class AdService {
  static showRewardedAd(rewardDetails: RewardDetails) {
    
    const message: NativeMessagePayload<RewardDetails> = {
      action: "RewardedAd",
      data: rewardDetails,
    };
    window.AndroidBridge?.postMessage(JSON.stringify(message));
  }

  static showInterstitialAd() {
    const message: NativeMessagePayload<RewardDetails> = {
      action: "InterstitialAd",
    };
    window.AndroidBridge?.postMessage(JSON.stringify(message));
  }

  static listen(callback: (action: string, data?: unknown) => void) {
    window.onNativeMessage = (payload: NativeMessagePayload) => {
      try {
        const parsed: NativeMessagePayload = typeof payload === 'string'
          ? JSON.parse(payload)
          : payload;

        callback(parsed.action, parsed.data);
      } catch (error) {
        console.error("Error parsing native message:", error);
      }
    };
  }

  static cleanup() {
    window.onNativeMessage = undefined;
  }
}

export default AdService;
