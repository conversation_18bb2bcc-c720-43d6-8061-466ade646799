import React, { useState, useEffect } from 'react';
import { X, Bell, Volume2, Music, Moon, Sun, Globe, Lock, Shield, Smartphone, Mail, User } from 'lucide-react';
import TwoFactorAuth from './TwoFactorAuth';
import TrustedDevices from './TrustedDevices';
import ChangeEmail from './ChangeEmail';
import ProfileSettings from './ProfileSettings';
import useFetch from '../hooks/useFetch';
import { GET_USER_PROFILE } from '../api/auth';
import { useAuth } from '../auth/AuthContext';
import { IMAGES } from '../constant/image';
import { useSocketContext } from '../context/socketProvider';

interface SettingsProps {
  onClose: () => void;
}


/**
 * Gets the level-specific image based on user level (0-12)
 */
const getLevelImage = (level: number): string => {
  // Map levels to corresponding MAP images
  const levelImageMap: { [key: number]: string } = {

    0: IMAGES.MAP_12,   // Level 0 -> MAP_1
    1: IMAGES.MAP_1,   // Level 1 -> MAP_1 (SHOT CALLER)
    2: IMAGES.MAP_2,   // Level 2 -> MAP_2 (CORNER HUSTLER)
    3: IMAGES.MAP_3,   // Level 3 -> MAP_3 (BORDER RUNNER)
    4: IMAGES.MAP_4,   // Level 4 -> MAP_4 (STREET BOSS)
    5: IMAGES.MAP_5,   // Level 5 -> MAP_5 (UNDERBOSS)
    6: IMAGES.MAP_6,   // Level 6 -> MAP_6 (OG)
    7: IMAGES.MAP_7,   // Level 7 -> MAP_7 (CONNECTOR)
    8: IMAGES.MAP_8,   // Level 8 -> MAP_8 (KINGPIN)
    9: IMAGES.MAP_9,   // Level 9 -> MAP_9 (STREET SCOUT)
    10: IMAGES.MAP_10, // Level 10 -> MAP_10 (LEGENDARY TOPDOG)
    11: IMAGES.MAP_11, // Level 11 -> MAP_11 (CAPO)
    12: IMAGES.MAP_11  // Level 12 -> MAP_11 (max level)
  };


  // Return the level-specific image or default to MAP_1 for invalid levels
  return levelImageMap[level] || IMAGES.MAP_1;
};

/**
 * Gets the level title based on user level (0-12)
 */
const getLevelTitle = (level: number): string => {
  const levelTitleMap: { [key: number]: string } = {
    0: 'SHOT CALLER',
    1: 'SHOT CALLER',
    2: 'CORNER HUSTLER',
    3: 'BORDER RUNNER',
    4: 'STREET BOSS',
    5: 'UNDERBOSS',
    6: 'OG',
    7: 'CONNECTOR',
    8: 'KINGPIN',
    9: 'STREET SCOUT',
    10: 'LEGENDARY TOPDOG',
    11: 'CAPO',
  };

  return levelTitleMap[level] || 'NEWCOMER';
};
const Settings: React.FC<SettingsProps> = ({ onClose }) => {
  const { accessToken } = useAuth();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [musicEnabled, setMusicEnabled] = useState(true);
  const [darkMode, setDarkMode] = useState(true);
  const [show2FA, setShow2FA] = useState(false);
  const [showTrustedDevices, setShowTrustedDevices] = useState(false);
  const [showChangeEmail, setShowChangeEmail] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);


      const {
        levelPlayerState,
        connectLevelPlayerSocket,
      } = useSocketContext();
  // Fetch user profile data
  const { data: userProfile } = useFetch(GET_USER_PROFILE, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Trigger animation
    setTimeout(() => setIsAnimating(true), 50);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);



  // Mobile bottom sheet styles
  const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const mobileContentClass = `
    bg-gradient-to-b from-[#510957] to-black
    rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out
    ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;

  // Desktop modal styles
  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const desktopContentClass = `
    bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
    rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col
    transform transition-all duration-300 ease-out
    ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
  `;

  return (
    <>
      <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
        <div className={isMobile ? mobileContentClass : desktopContentClass}>
          <div className="p-6 flex justify-between items-center sticky top-0 bg-gradient-to-r from-[#510957] to-[#510957] rounded-t-3xl md:rounded-t-2xl z-10">
            <h2 className="text-xl text-white font-[Anton] tracking-wide">Settings</h2>
            <button
              onClick={onClose}
              className="text-white/60 hover:text-white transition-colors"
            >
              <X size={24} />
            </button>
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="p-6 space-y-6">
              {/* Profile Section */}
              <section>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 font-[Anton] tracking-wide">
                  <User size={18} className="text-[#ED0CFF]" />
                  Profile
                </h3>
                <button
                  onClick={() => setShowProfile(true)}
                  className="w-full flex items-center justify-between p-4 bg-[#131313] rounded-lg transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <img
                      src={getLevelImage(levelPlayerState?.level)}
                      alt="Profile"
                      className="w-10 h-10 rounded-full"
                    />
                    <div className="text-left">
                      <p className="font-semibold text-xs">Player ID: {userProfile?.user_id}</p>
                      <p className="text-sm text-white/60">{userProfile?.email || "Email"}</p>
                    </div>
                  </div>
                  <span className="text-sm text-white font-[Anton] tracking-wide">Edit</span>
                </button>
              </section>

              {/* Notifications */}
              <section>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 font-[Anton] tracking-wide">
                  <Bell size={18} className="text-white" />
                  Notifications
                </h3>
                <div className="space-y-4 w-full items-center justify-between p-4 bg-[#131313] rounded-lg transition-colors">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium font-[Anton] tracking-wide">Push Notifications</p>
                      <p className="text-sm text-white/60">Receive game alerts and updates</p>
                    </div>
                    <button
                      onClick={() => setNotificationsEnabled(!notificationsEnabled)}
                      className={`w-12 h-6 rounded-full transition-colors ${notificationsEnabled ? 'bg-[#ED0CFF]' : 'bg-white/10'}`}
                    >
                      <div className={`w-5 h-5 rounded-full bg-white transform transition-transform ${notificationsEnabled ? 'translate-x-6' : 'translate-x-1'}`} />
                    </button>
                  </div>
                </div>
              </section>

              {/* Sound Settings */}
              <section>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 font-[Anton] tracking-wide">
                  <Volume2 size={18} className="text-white" />
                  Sound
                </h3>
                <div className="space-y-4 w-full items-center justify-between p-4 bg-[#131313] rounded-lg transition-colors">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium font-[Anton] tracking-wide">Game Sounds</p>
                      <p className="text-sm text-white/60">Enable in-game sound effects</p>
                    </div>
                    <button
                      onClick={() => setSoundEnabled(!soundEnabled)}
                      className={`w-12 h-6 rounded-full transition-colors ${soundEnabled ? 'bg-[#ED0CFF]' : 'bg-white/10'}`}
                    >
                      <div className={`w-5 h-5 rounded-full bg-white transform transition-transform ${soundEnabled ? 'translate-x-6' : 'translate-x-1'}`} />
                    </button>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium font-[Anton] tracking-wide">Background Music</p>
                      <p className="text-sm text-white/60">Enable background music</p>
                    </div>
                    <button
                      onClick={() => setMusicEnabled(!musicEnabled)}
                      className={`w-12 h-6 rounded-full transition-colors ${musicEnabled ? 'bg-[#ED0CFF]' : 'bg-white/10'}`}
                    >
                      <div className={`w-5 h-5 rounded-full bg-white transform transition-transform ${musicEnabled ? 'translate-x-6' : 'translate-x-1'}`} />
                    </button>
                  </div>
                </div>
              </section>

              {/* Theme Settings */}
              <section>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 font-[Anton] tracking-wide">
                  {darkMode ? <Moon size={18} className="text-white" /> : <Sun size={18} className="text-white" />}
                  Theme
                </h3>
                <div className="space-y-4 w-full items-center justify-between p-4 bg-[#131313] rounded-lg transition-colors">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium font-[Anton] tracking-wide">Dark Mode</p>
                      <p className="text-sm text-white/60">Enable dark theme</p>
                    </div>
                    <button
                      onClick={() => setDarkMode(!darkMode)}
                      className={`w-12 h-6 rounded-full transition-colors ${darkMode ? 'bg-[#ED0CFF]' : 'bg-white/10'}`}
                    >
                      <div className={`w-5 h-5 rounded-full bg-white transform transition-transform ${darkMode ? 'translate-x-6' : 'translate-x-1'}`} />
                    </button>
                  </div>
                </div>
              </section>

              {/* Security */}
              <section>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 font-[Anton] tracking-wide">
                  <Lock size={18} className="text-white" />
                  Security
                </h3>
                <div className="space-y-4">
                  <button
                    onClick={() => setShow2FA(true)}
                    className="w-full flex items-center justify-between p-4 bg-[#131313] rounded-lg transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="bg-[#1c1c1c] p-3 rounded-lg">
                        <Shield size={18} className="text-[#ED0CFF]" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium font-[Anton] tracking-wide">Two-Factor Authentication</p>
                        <p className="text-sm text-white/60">Add extra security to your account</p>
                      </div>
                    </div>
                    <div className="text-sm text-white/40">Not Enabled</div>
                  </button>

                  <button
                    onClick={() => setShowTrustedDevices(true)}
                    className="w-full flex items-center justify-between p-4 bg-[#131313] rounded-lg transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="bg-[#1c1c1c] p-3 rounded-lg">
                        <Smartphone size={18} className="text-[#ED0CFF]" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium font-[Anton] tracking-wide">Trusted Devices</p>
                        <p className="text-sm text-white/60">Manage your trusted devices</p>
                      </div>
                    </div>
                  </button>
                </div>
              </section>

              {/* Account */}
              <section>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 font-[Anton] tracking-wide">
                  <Mail size={18} className="text-white" />
                  Account
                </h3>
                <div className="space-y-4">
                  <button
                    onClick={() => setShowChangeEmail(true)}
                    className="w-full flex items-center justify-between p-4 bg-[#131313] rounded-lg transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="bg-[#1c1c1c] p-3 rounded-lg">
                        <Mail size={18} className="text-[#ED0CFF]" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium font-[Anton] tracking-wide">Change Email</p>
                        <p className="text-sm text-white/60">Update your email address</p>
                      </div>
                    </div>
                  </button>

                  <button className="w-full flex items-center justify-between p-4 bg-[#131313] rounded-lg transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#1c1c1c] p-3 rounded-lg">
                        <Lock size={18} className="text-[#ED0CFF]" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium font-[Anton] tracking-wide">Change Password</p>
                        <p className="text-sm text-white/60">Update your password</p>
                      </div>
                    </div>
                  </button>
                </div>
              </section>
            </div>
          </div>
        </div>
      </div>

      {/* 2FA Modal */}
      {show2FA && (
        <TwoFactorAuth onClose={() => setShow2FA(false)} data={userProfile} />
      )}

      {/* Trusted Devices Modal */}
      {showTrustedDevices && (
        <TrustedDevices onClose={() => setShowTrustedDevices(false)} />
      )}

      {/* Change Email Modal */}
      {showChangeEmail && (
        <ChangeEmail onClose={() => setShowChangeEmail(false)} />
      )}

      {/* Profile Settings Modal */}
      {showProfile && (
        <ProfileSettings onClose={() => setShowProfile(false)} />
      )}
    </>
  );
};

export default Settings;