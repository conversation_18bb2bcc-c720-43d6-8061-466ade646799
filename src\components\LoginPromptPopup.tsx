import React from 'react';
import { X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface LoginPromptPopupProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
}

const LoginPromptPopup: React.FC<LoginPromptPopupProps> = ({
  isOpen,
  onClose,
  title = "Join the Hustle!",
  message = "Create an account or login to start playing games and earning rewards. Your journey from streets to success begins here!"
}) => {
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleLoginClick = () => {
    navigate('/auth');
    onClose();
  };

  const handleSignupClick = () => {
    navigate('/auth');
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm">
      <div className="bg-[#131313] rounded-2xl shadow-2xl p-6 max-w-sm w-full mx-4 relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-white/60 hover:text-white transition-colors"
        >
          <X size={20} />
        </button>

        {/* Content */}
        <div className="text-center">
          {/* Icon or Image */}
          <div className="mb-4">
            <div className="w-16 h-16 bg-[#ED0CFF]/20 rounded-full flex items-center justify-center mx-auto">
              <span className="text-2xl">🎮</span>
            </div>
          </div>

          {/* Title */}
          <h2 className="text-xl font-bold text-white mb-3 font-[Anton]">
            {title}
          </h2>

          {/* Message */}
          <p className="text-white/80 text-sm mb-6 leading-relaxed">
            {message}
          </p>

          {/* Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleSignupClick}
              className="w-full bg-[#ED0CFF] text-white py-3 rounded-lg font-semibold hover:bg-[#ED0CFF]/80 transition-colors"
            >
              Sign Up Now
            </button>
            <button
              onClick={handleLoginClick}
              className="w-full border border-white/20 text-white py-3 rounded-lg font-semibold hover:bg-white/10 transition-colors"
            >
              Login
            </button>
          </div>

          {/* Additional Text */}
          <p className="text-white/50 text-xs mt-4">
            Join thousands of players already earning rewards!
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPromptPopup;
