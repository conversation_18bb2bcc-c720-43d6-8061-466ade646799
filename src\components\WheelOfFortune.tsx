import React, { useState, useEffect } from 'react';
import { Gift, Package } from 'lucide-react';
import winSound from '../Sounds/WheelofFortune/WoFWin.mov';
import BackGroundSound from '../Sounds/WheelofFortune/Wheen_of_Fortune_Spin.mp3';
import SoundManager from '../components/soundManager/SoundManager';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  fetchSpinningWheelConfig,
  getSpinningWheelConfigs,
  getWheelPrice,
  SpinningWheelConfig,
  SpinningWheelResponse,
} from '../api/wheelSpeenService';
import { useAuth } from '../auth/AuthContext';
import { useCoins } from '../hooks/useCoinsQuery';
import { PzPopup } from './Shared/PzPopup';
import { PzButton } from './Shared/PzButton';
import { IMAGES } from '../constant/image';
import { preloadImage } from '../utils/helper';
import { useGameDataLoading } from '../hooks/useGameDataLoading';
import { GameLoadingScreen } from './GameLoadingScreen/GameLoadingScreen';
import { CustomToast } from '../utils/validations/customeToast';
interface WheelOfFortuneProps {
  onClose: () => void;
  onWin: (reward: {
    type: 'coins' | 'fpp' | 'xp' | 'mystery_box' | 'airtime' | 'merch';
    amount: number;
  }) => void;
}

const MYSTERY_REWARDS = [
  { type: 'fpp' as const, amount: 100, label: '100 Points!' },
  { type: 'airtime' as const, amount: 1, label: 'Airtime Bundle!' },
  { type: 'coins' as const, amount: 0, label: 'Empty Box!' },
];

const mockedWheelData = [
  {
    text: '1GB',
    color1: '#0CC6FF',
    color2: '#005CB8',
    image: IMAGES.INTERNET_TRAFFIC,
  },
  { text: '$500', color1: '#282828', color2: '#000000', image: IMAGES.DOLLAR },
  {
    text: 'Mystery',
    color1: '#FFCE0C',
    color2: '#B89300',
    image: IMAGES.MYSTERY,
  },
  {
    text: '$1,000',
    color1: '#282828',
    color2: '#000000',
    image: IMAGES.DOLLAR,
  },
  {
    text: '$5,000',
    color1: '#282828',
    color2: '#000000',
    image: IMAGES.DOLLAR,
  },
  {
    text: 'Mystery',
    color1: '#FFCE0C',
    color2: '#B89300',
    image: IMAGES.MYSTERY,
  },
  {
    text: 'Better',
    color1: '#ED0CFF',
    color2: '#9400A0',
    image: IMAGES.BETTER,
  },
  {
    text: 'Baseball cap',
    color1: '#FFFFFF',
    color2: '#A8A8A8',
    image: IMAGES.BASEBALL_CAP,
  },
  { text: '$100', color1: '#282828', color2: '#000000', image: IMAGES.DOLLAR },
  {
    text: 'Better',
    color1: '#ED0CFF',
    color2: '#9400A0',
    image: IMAGES.BETTER,
  },
  {
    text: 'Mystery',
    color1: '#FFCE0C',
    color2: '#B89300',
    image: IMAGES.MYSTERY,
  },
  {
    text: 'Better',
    color1: '#ED0CFF',
    color2: '#9400A0',
    image: IMAGES.BETTER,
  },
];

const ANIMATION_TIME = 6000;

const WheelOfFortune: React.FC<WheelOfFortuneProps> = ({ onClose, onWin }) => {
  const [isSpinning, setIsSpinning] = useState<boolean>(false);
  const { accessToken } = useAuth();
  const { coins } = useCoins();
  const [rotation, setRotation] = useState(0);
  const [showReward, setShowReward] = useState<boolean>(false);
  const [selectedReward, setSelectedReward] = useState<
    SpinningWheelConfig[] | undefined
  >();
  const [showMysteryBox, setShowMysteryBox] = useState(false);
  const [mysteryReward, setMysteryReward] = useState<
    (typeof MYSTERY_REWARDS)[0] | null
  >(null);
  const [winData, setWinData] = useState<
    (SpinningWheelResponse & { winningIndex?: number }) | null
  >(null);
  const [didWin, setDidWin] = useState(false);
  const [showPurchaseError, setShowPurchaseError] = useState(false);
  const [cost, setCost] = useState<number>(0);
  const [targetIndex, setTargetIndex] = useState<number>(0);
  const [transition, setTransition] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  const {
    data: wheelConfig,
    isLoading: isWheelConfigLoading,
    isError: isConfigError,
  } = useQuery({
    queryKey: ['wheelConfig'],
    queryFn: () => {
      if (!accessToken) throw new Error('Access token missing');
      return getSpinningWheelConfigs(1, 10, String(accessToken));
    },
    refetchOnMount: true,
    enabled: !!accessToken,
  });

  const {
    data: wheelPrice,
    isLoading: isWheelPriceLoading,
    isError: isPriceError,
  } = useQuery({
    queryKey: ['wheelPrice'],
    queryFn: () => {
      if (!accessToken) throw new Error('Access token missing');
      return getWheelPrice(accessToken);
    },
    refetchOnMount: true,
    enabled: !!accessToken,
  });

  const placeBetMutation = useMutation<SpinningWheelResponse>({
    mutationFn: async () => {
      if (!accessToken) throw new Error('Access token missing');
      const data = await fetchSpinningWheelConfig(accessToken);
      setWinData(data);
      return data;
    },
  });

  const { isGameDataLoading, visibility } = useGameDataLoading(
    Number.isFinite(coins),
    !isWheelConfigLoading,
    !isWheelPriceLoading
  );

  useEffect(() => {
    // Preload images to optimize loading
    [
      IMAGES.LIGHT,
      IMAGES.WHEEL_SPEAR,
      IMAGES.SEGMENT_BORDER,
      IMAGES.BASEBALL_CAP,
      IMAGES.BETTER,
      IMAGES.DOLLAR,
      IMAGES.INTERNET_TRAFFIC,
      IMAGES.MYSTERY,
      IMAGES.WHEEL_BORDERS,
    ].forEach(preloadImage);
  }, []);

  useEffect(() => {
    const mockedWheelConfig = wheelConfig
      ? [...wheelConfig, wheelConfig?.[0], wheelConfig?.[1]]
      : [];
    setSelectedReward(mockedWheelConfig);
    return () => {
      setIsSpinning(false);
      setShowReward(false);
      setDidWin(false);
      setShowMysteryBox(false);
    };
  }, [wheelConfig]);

  useEffect(() => {
    wheelPrice?.price && setCost(wheelPrice.price);
  }, [wheelPrice]);

  useEffect(() => {
    if (coins < cost) {
      setShowPurchaseError(true);
    }
  }, [cost]);

  useEffect(() => {
    isPriceError && CustomToast('error', 'Failed to get price');
    isConfigError && CustomToast('error', 'Failed to get configuration');
  }, [isPriceError, isConfigError]);

  const rotateToIndex = (targetIndex = 0) => {
    const customAngleShift = 12;

    const segmentSize = selectedReward?.length
      ? 360 / mockedWheelData.length
      : 0;
    const targetRotation = 360 - (rotation % 360) - targetIndex * segmentSize;
    const totalRotation = 360 - 90 - customAngleShift + targetRotation;

    setRotation((prev) => prev + totalRotation);
    setTransition('transform 6s linear');
  };

  const spinWheel = () => {
    if (isSpinning) return;
    setIsSpinning(true);
    setIsProcessing(true);
    setDidWin(false);
    setShowMysteryBox(false);
    setMysteryReward(null);

    // Start rotation before placeBetMutation to avoid the pause break
    setTransition('transform 3s linear');
    setRotation((prev) => prev + 360);

    // 1️⃣ Call bet API
    placeBetMutation.mutate(undefined, {
      onSuccess: (result) => {
        const winningIndex =
          selectedReward?.findIndex(
            (segment) => result.data.id === segment?.id
          ) || 0;

        setTargetIndex(winningIndex);
        setWinData({ ...result, winningIndex });

        setTimeout(() => setShowReward(false), ANIMATION_TIME * 1.8);

        const prizeAmount = Number(result?.data?.prize.match(/\d+/));

        rotateToIndex(winningIndex);

        setTimeout(() => {
          setShowMysteryBox(true);
          setShowReward(true);
          setDidWin(true);
          setIsProcessing(false);

          onWin?.({
            type: 'coins',
            amount: Number(
              prizeAmount
                ? prizeAmount - Number(result?.data?.bet_amount)
                : result?.data?.bet_amount
            ),
          });
        }, ANIMATION_TIME + 1000);
        setTimeout(() => {
          setIsSpinning(false);
        }, ANIMATION_TIME);
      },
      onError: () => CustomToast('error', 'Failed to place bet.'),
    });
  };

  return (
    <>
      {(isGameDataLoading || !selectedReward?.length) && (
        <GameLoadingScreen isOuter />
      )}

      <div
        style={{
          visibility:
            visibility && selectedReward?.length ? visibility : 'hidden',
        }}
      >
        <SoundManager
          sounds={{
            background: BackGroundSound,
            win: winSound,
          }}
          // loop the background while the game is running:
          backgroundKey={isSpinning ? 'background' : null}
          // play one of these exactly once when it changes:
          playKey={didWin ? 'win' : undefined}
          volumes={{
            background: 0.4,
            win: 1.0,
          }}
        />
        {showReward ? (
          <PzPopup onClose={() => setShowReward(false)}>
            <div className="text-center">
              <img
                src={mockedWheelData[targetIndex].image}
                width={130}
                alt="Win image"
                className="m-auto"
              />
              <h3
                className="text-white text-4xl my-2"
                style={{
                  fontFamily: 'Bebas Neue',
                }}
              >
                CONGRATULATIONS!
              </h3>
              <p className="text-white font-[Anton] text-sm mb-8">
                {winData?.data.prize}
              </p>
              <PzButton
                text="Take it!"
                onClick={() => {
                  setShowReward(false);
                  // Wait for previous state change to keep canvas animation on track
                  setTimeout(spinWheel, 0);
                }}
              />
            </div>
          </PzPopup>
        ) : (
          <PzPopup title="Carnival Wheel" onClose={onClose}>
            <div className="relative">
              <div className="relative aspect-square mb-8 mt-8">
                <svg
                  viewBox="-25 -25 450 450"
                  className="w-full filter drop-shadow-xl"
                >
                  <circle
                    cx="200"
                    cy="200"
                    r="207"
                    fill="none"
                    stroke="white"
                    strokeWidth="10"
                  />
                  <circle
                    cx="200"
                    cy="200"
                    r="193"
                    fill="none"
                    stroke="black"
                    strokeWidth="25"
                  />

                  {new Array(mockedWheelData.length)
                    .fill(IMAGES.LIGHT)
                    .map((image, index) => {
                      const baseAngle = 190;
                      const imageShift = 10;
                      const angleShift = baseAngle + 2;
                      const angle = (index * 360) / 12;
                      const rad = (angle * Math.PI) / 180;
                      const x = 190 + Math.cos(rad) * angleShift - imageShift;
                      const y = 190 + Math.sin(rad) * angleShift - imageShift;
                      return (
                        <image
                          key={index}
                          href={image}
                          width="40"
                          x={x}
                          y={y}
                        />
                      );
                    })}

                  <svg
                    style={{
                      willChange: 'transform',
                      transform: `rotate(${rotation}deg)`,
                      transformOrigin: '200px 200px',
                      ...(isSpinning && { transition }),
                    }}
                  >
                    {selectedReward?.map((reward, index) => {
                      const devider = selectedReward.length;
                      const angle = (index * 360) / devider;
                      const nextAngle = ((index + 1) * 360) / devider;
                      const midAngle = (angle + nextAngle) / 2;
                      const radMid = (midAngle * Math.PI) / 180;

                      const textRadius = 120;
                      const textX = 200 + Math.cos(radMid) * textRadius;
                      const textY = 200 + Math.sin(radMid) * textRadius;

                      const { color1, color2 } = mockedWheelData[index];

                      return (
                        <g key={index}>
                          <defs>
                            <linearGradient
                              id={`segment-gradient-${index}`}
                              x1="0%"
                              y1="0%"
                              x2="100%"
                              y2="100%"
                            >
                              <stop
                                offset="0%"
                                stopColor={color1}
                                stopOpacity="1"
                              />
                              <stop
                                offset="100%"
                                stopColor={color2}
                                stopOpacity="0.7"
                              />
                            </linearGradient>
                          </defs>
                          <path
                            d={`M 200 200
                          L ${200 + 180 * Math.cos((angle * Math.PI) / 180)} ${
                              200 + 180 * Math.sin((angle * Math.PI) / 180)
                            }
                          A 180 180 0 0 1 ${
                            200 + 180 * Math.cos((nextAngle * Math.PI) / 180)
                          } ${200 + 180 * Math.sin((nextAngle * Math.PI) / 180)}
                          Z`}
                            fill={`url(#segment-gradient-${index})`}
                          />
                          <g
                            transform={`translate(${textX} ${textY}) rotate(${
                              angle + 15
                            }) scale(${
                              angle > 60 && angle < 270 ? '-1, -1' : '1, 1'
                            })`}
                          >
                            <text
                              fill="white"
                              fontSize="20"
                              fontFamily="Anton, Sans-serif"
                              fontWeight="bold"
                              textAnchor="middle"
                              dominantBaseline="middle"
                              className="outlined-text"
                            >
                              {reward.name}
                            </text>
                          </g>
                        </g>
                      );
                    })}
                    <image
                      href={IMAGES.WHEEL_BORDERS}
                      width="620"
                      x="-110"
                      y="-105"
                      transform="rotate(1, 200, 200)"
                    />
                  </svg>
                  <image
                    href={IMAGES.WHEEL_SPEAR}
                    width="500"
                    x="-47"
                    y="-50"
                  />
                </svg>

                <div
                  style={{
                    width: '80%',
                    height: '80%',
                    borderRadius: '50%',
                    boxShadow: 'rgb(0, 0, 0) 0px 0px 20px 3px inset',
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                  }}
                />

                {showReward && selectedReward && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm rounded-xl animate-fade-in">
                    <div className="text-center">
                      {winData?.data?.prize?.toLowerCase() !== 'point 0' ? (
                        <>
                          <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce">
                            {winData?.data?.prize?.toLowerCase() ===
                            'points' ? (
                              <Gift size={40} className="text-white" />
                            ) : (
                              <span className="text-4xl">🎪</span>
                            )}
                          </div>
                          <p className="text-3xl font-bold text-white mb-2">
                            {winData?.data?.prize?.toLowerCase() === 'points'
                              ? 'Mystery Box!'
                              : 'Congratulations! 🎉'}
                          </p>
                          <p className="text-xl text-white">
                            {winData?.data?.prize}
                          </p>
                        </>
                      ) : (
                        <>
                          <div className="w-20 h-20 bg-gray-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span className="text-4xl">🎯</span>
                          </div>
                          <p className="text-2xl font-bold text-white mb-2">
                            Better luck next time!
                          </p>
                          <p className="text-lg text-white/60">
                            Keep spinning for a chance to win!
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                )}

                {showMysteryBox && mysteryReward && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm rounded-xl animate-fade-in">
                    <div className="text-center">
                      <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-xl flex items-center justify-center mx-auto mb-4 animate-bounce">
                        <Package size={48} className="text-white" />
                      </div>
                      <p className="text-3xl font-bold text-white mb-3">
                        Mystery Box Opened! 🎁
                      </p>
                      <p className="text-xl text-white">
                        You won:{' '}
                        <span className="text-yellow-400">
                          {mysteryReward.label}
                        </span>
                      </p>
                    </div>
                  </div>
                )}
              </div>

              <PzButton
                text={isProcessing ? 'Spinning...' : 'Spin the Wheel!'}
                isDisabled={isProcessing}
                onClick={spinWheel}
              />
              <div className="text-sm m-auto text-center mt-2 font-[Anton]">
                Spin the wheel for {cost} bucks
              </div>
            </div>
          </PzPopup>
        )}

        {showPurchaseError && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div
              className="rounded-xl bg-black/90 p-6 w-full max-w-xs mx-auto"
              style={{
                backgroundImage: `url(${IMAGES.ECLIPSE_BG})`,
              }}
            >
              {showPurchaseError && (
                <p className="text-red-400 my-4">
                  Insufficient bucks. You need {cost} bucks to purchase.
                </p>
              )}
              <PzButton type="secondary" text="Cancel" onClick={onClose} />
            </div>
          </div>
        )}

        <style>{`
        @keyframes carnival-lights {
          0%, 100% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.5; transform: scale(0.8); }
        }
        .animate-carnival-lights {
          animation: carnival-lights 1s ease-in-out infinite;
        }
        @keyframes gentle-bounce {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(2px); }
        }
      `}</style>
      </div>
    </>
  );
};

export default WheelOfFortune;
