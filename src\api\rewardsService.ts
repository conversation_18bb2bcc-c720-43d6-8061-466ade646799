import axios from 'axios'

export interface RewardClaimPayload {
  user_id: string
  game: string
  position?: number
  reward_type: string
  amount: number
  source: string
  transaction_id?: string
  wallet_id?: string
}

const API_BASE_URL = process.env.VITE_REWARD_BASE_URL

export const postRewardClaim = async (payload: RewardClaimPayload): Promise<void> => {
  try {
    const finalPayload: RewardClaimPayload = {
      ...payload,
      source: 'web-app',
    }

    await axios.post(`${API_BASE_URL}/reward-claim`, finalPayload, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.VITE_REWARD_API_KEY || '',
      },
    })
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error('Failed to post reward claim:', error.response?.data || error.message)
    throw error
  }
}
