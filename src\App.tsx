import React, { useEffect, useState } from 'react';
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from 'react-router-dom';
import Layout from './components/Layout';
import Home from './pages/Home';
import Shop from './pages/Shop';
import Wallet from './pages/Wallet';
import Rewards from './pages/Rewards';
import Leaderboard from './pages/Leaderboard';
import Squads from './pages/Squads';
import Auth from './pages/Auth';
import StoryIntro from './components/StoryIntro';
import CrashGame from './pages/CrashGame';
import CrashGame2 from './pages/CrashGame2';
import CryptoKing from './pages/CryptoKing';
import HigherLowerGame from './pages/HigherLowerGame';
import RollDiceGame from './pages/RollDiceGame';
import Map from './pages/Map';
import ScratchCard from './components/ScratchCard';
import BoostModal from './components/BoostModal';
import WheelOfFortune from './components/WheelOfFortune';
// import ProtectedRoute from './components/ProtectedRoute';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useAuth } from './auth/AuthContext';
import { GET_BALANCE_API } from './api/auth';
import { useSocketContext } from './context/socketProvider';
import PlinkoGame from './pages/PlinkoGame';
import TermsAndConditionsPage from './components/TermsAndConditions';
import PrivacyPolicyPage from './components/PrivacyPolicy';
import { useGameDataLoading } from './hooks/useGameDataLoading';

import AdService from './api/addService';
function App() {
  const [balance, setBalance] = useState({
    coins: 0,
    fpp: 0,
    level: 0,
    xp: 0,
    real_money: '0',
    bucks_wagered: 0,
    bucks_required: 0,
  });
  const { connectSocket, isConnected } = useSocketContext();
  useGameDataLoading();

  const { userProfile, loadUserProfile, isAuthenticated, login, accessToken } =
    useAuth();
  const [showStoryIntro, setShowStoryIntro] = useState(false);
  const [showScratchCard, setShowScratchCard] = useState(false);
  const [showBoostModal, setShowBoostModal] = useState(false);
  const [showWheel, setShowWheel] = useState(false);
  const [data, setData] = useState<any[]>([]);

  const fetchBalance = () =>
    fetch(GET_BALANCE_API, {
      headers: { Authorization: `Bearer ${accessToken}` },
    })
      .then((res) => {
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        return res.json();
      })
      .then(
        (
          data: {
            bonus_money: string;
            currency: string;
            id: string;
            real_money: string;
            updated_at: string;
            user_id: string;
          }[]
        ) => {
          const coins = Number(
            data.find((balance) => balance.currency === 'P')?.real_money
          );
          setData(data);
          setBalance({ ...balance, coins });
        }
      )
      .catch((err) => console.error('Balance fetch failed:', err));

  useEffect(() => {
    if (!isAuthenticated || !accessToken) return;

    loadUserProfile();
    fetchBalance();
  }, [isAuthenticated, accessToken]);

  useEffect(() => {
    const hasSeenIntro = window.localStorage.getItem('hasSeenStoryIntro');

    if (!hasSeenIntro) {
      const timer = setTimeout(() => setShowStoryIntro(true), 500);
      return () => clearTimeout(timer);
    }
  }, []);
  useEffect(() => {
    if (!isConnected) {
      connectSocket();
    }
  }, [isConnected]);

  const handleLogin = async (token: string) => {
    login(token);

    // setShowStoryIntro(true);
  };

  // Listen for custom events
  React.useEffect(() => {
    const handleShowScratchCard = () => {
      setShowScratchCard(true);
    };

    const handleShowBoostModal = () => {
      setShowBoostModal(true);
    };

    const handleShowWheel = () => {
      setShowWheel(true);
    };

    window.addEventListener('show-scratch-card', handleShowScratchCard);
    window.addEventListener('show-boost-modal', handleShowBoostModal);
    window.addEventListener('show-wheel', handleShowWheel);

    return () => {
      window.removeEventListener('show-scratch-card', handleShowScratchCard);
      window.removeEventListener('show-boost-modal', handleShowBoostModal);
      window.removeEventListener('show-wheel', handleShowWheel);
      AdService.cleanup();
    };
  }, []);

  const handleWin = (reward: { type: string; amount?: number }) => {
    if (reward.type === 'coins') {
      fetchBalance();
    } else if (reward.type === 'fpp') {
      setBalance((prev) => ({
        ...prev,
        fpp: prev.fpp + Number(reward.amount),
      }));
    }
  };

  const onFakePurchase = (cost: number) =>
    setBalance({ ...balance, coins: balance.coins - cost });

  const onPurchase = () => fetchBalance();

  const handleShare = (platform: string) => {
    // Implement share functionality
    setShowBoostModal(false);
    // Add Bucks reward for sharing
    setBalance((prev) => ({
      ...prev,
      xp: Math.min(100, prev.xp + 10),
    }));
  };

  // --- Handle rewarded ad events ---
  useEffect(() => {
    AdService.listen((action) => {
      if (action === 'rewardAdClaimed') {
        console.log('Rewarded Ad claimed');
      } else if (action === 'rewardAdClaimIneligible') {
        console.log('Ineligible for reward');
      } else if (
        action === 'rewardAdClaimFailed' ||
        action === 'rewardedAdFailed'
      ) {
        console.log('Reward ad error');
      } else if (action === 'rewardAdCompleted') {
        console.log('Rewarded Ad completed');
      }
    });
  }, []);

  console.log('userProfile', userProfile);

  return (
    // <SocketProvider>
    <Router>
      <Routes>
        <Route
          path="/auth"
          element={
            isAuthenticated ? (
              <Navigate to="/" replace />
            ) : (
              <Auth onLogin={handleLogin} />
            )
          }
        />
        <Route
          path="/"
          element={
            <>
              <Layout balance={balance} />
              {showStoryIntro && (
                <div className="fixed inset-0 bg-black z-50">
                  <StoryIntro onClose={() => setShowStoryIntro(false)} />
                </div>
              )}
              {showScratchCard && (
                <ScratchCard
                  onClose={() => setShowScratchCard(false)}
                  onWin={handleWin}
                  // cost={100}
                  balance={balance.coins}
                  onPurchase={(cost) => {
                    setBalance((prev) => ({
                      ...prev,
                      coins: prev.coins - cost,
                    }));
                  }}
                />
              )}
              {showBoostModal && (
                <BoostModal
                  onClose={() => setShowBoostModal(false)}
                  onWatchAd={() => {
                    setShowBoostModal(false);
                    if (userProfile) {
                      AdService.showRewardedAd({
                        userId: userProfile?.user_id,
                      });
                    }
                    // Handle video ad
                  }}
                  onShare={handleShare}
                />
              )}
              {showWheel && (
                <WheelOfFortune
                  onClose={() => setShowWheel(false)}
                  onWin={handleWin}
                />
              )}
              <ToastContainer position="top-right" />
            </>
          }
        >
          <Route
            path="/terms-and-conditions"
            element={<TermsAndConditionsPage />}
          />
          <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
          <Route
            index
            element={<Home balance={balance} onBalanceChange={(partial) => setBalance(prev => ({ ...prev, ...partial }))} />}
          />
          <Route
            path="shop"
            element={<Shop balance={balance} onBalanceChange={(partial) => setBalance(prev => ({ ...prev, ...partial }))} />}
          />
          <Route path="wallet" element={<Wallet balance={balance} />} />
          <Route path="squads" element={<Squads />} />
          <Route
            path="rewards"
            element={<Rewards balance={balance} onBalanceChange={(partial) => setBalance(prev => ({ ...prev, ...partial }))} />}
          />
          <Route path="leaderboard" element={<Leaderboard />} />
          <Route path="map" element={<Map currentLevel={balance.level} />} />
          <Route
            path="street-king"
            element={
              <CrashGame
                onWin={handleWin}
                onPurchase={onPurchase}
                balance={balance.coins}
              />
            }
          />
          <Route
            path="street-king-2"
            element={
              <CrashGame2
                onWin={handleWin}
                onPurchase={onPurchase}
                balance={balance.coins}
              />
            }
          />
          <Route
            path="crypto-king"
            element={
              <CryptoKing
                onPurchase={onFakePurchase}
                onWin={handleWin}
                balance={balance.coins}
              />
            }
          />
          <Route
            path="quick-hustle"
            element={
              <HigherLowerGame
                onWin={handleWin}
                onPurchase={onPurchase}
                balance={balance.coins}
              />
            }
          />
          <Route
            path="roll-dice"
            element={
              <RollDiceGame
                onPurchase={onFakePurchase}
                onWin={handleWin}
                balance={balance.coins}
              />
            }
          />
          <Route
            path="plinko"
            element={
              <PlinkoGame
                onPurchase={onFakePurchase}
                onWin={handleWin}
                balance={balance.coins}
              />
            }
          />
        </Route>
      </Routes>
    </Router>
    // </SocketProvider>
  );
}

export default App;
