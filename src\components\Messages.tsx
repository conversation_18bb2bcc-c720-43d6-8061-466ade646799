import React, { useEffect, useState } from 'react';
import { X, Bell, Gift, Coins, Star, MessageCircle } from 'lucide-react';

interface MessagesProps {
  onClose: () => void;
}

const MESSAGES = [
  {
    id: 1,
    type: 'reward',
    title: 'Daily Reward Available!',
    message: 'Claim your daily bonus of 100 coins now!',
    icon: Gift,
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-400/20',
    time: '2 hours ago',
    unread: true
  },
  {
    id: 2,
    type: 'achievement',
    title: 'New Achievement Unlocked!',
    message: 'You\'ve reached Level 3! Claim your reward of 500 FPP.',
    icon: Star,
    color: 'text-purple-400',
    bgColor: 'bg-purple-400/20',
    time: '1 day ago',
    unread: true
  },
  {
    id: 3,
    type: 'bonus',
    title: 'Special Weekend Bonus',
    message: 'Get 2x coins on all games this weekend!',
    icon: Coins,
    color: 'text-green-400',
    bgColor: 'bg-green-400/20',
    time: '2 days ago',
    unread: false
  },
  {
    id: 4,
    type: 'system',
    title: 'Welcome to <PERSON><PERSON><PERSON>!',
    message: 'Start your journey with 1500 free coins!',
    icon: MessageCircle,
    color: 'text-blue-400',
    bgColor: 'bg-blue-400/20',
    time: '3 days ago',
    unread: false
  }
];

const Messages: React.FC<MessagesProps> = ({ onClose }) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);

    setTimeout(() => setIsAnimating(true), 50);

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsAnimating(false);
        setTimeout(onClose, 300);
      }
    };
    window.addEventListener('keydown', handleEscape);

    return () => {
      window.removeEventListener('resize', checkMobile);
      window.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(onClose, 300);
  };

  // Mobile and desktop overlay/modal classes (copied from Wallet)
  const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;
  const mobileContentClass = `
    bg-gradient-to-b from-[#510957] to-black
    rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out
    ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;
  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;
  const desktopContentClass = `
    bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
    rounded-2xl w-full max-w-md max-h-[85vh] flex flex-col
    transform transition-all duration-300 ease-out
    ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
  `;

  return (
    <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
      <div className={isMobile ? mobileContentClass : desktopContentClass}>
        {/* Header */}
        <div className={`p-6 flex justify-between items-center sticky top-0 from-[#510957] to-[#510957] ${isMobile ? 'rounded-t-3xl' : 'rounded-t-2xl'} z-10`}>
          <div className="flex items-center gap-3">
            <Bell size={20} className="text-yellow-400" />
            <h2 className="text-xl font-bold text-white font-[Anton] tracking-wide">Messages</h2>
          </div>
          <button
            onClick={handleClose}
            className="text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-full"
          >
            <X size={24} />
          </button>
        </div>

        {/* Mobile handle bar */}
        {isMobile && (
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-1 bg-white/30 rounded-full"></div>
          </div>
        )}

        <div className="flex-1 overflow-y-auto divide-y divide-white/10">
          {MESSAGES.map(message => {
            const IconComponent = message.icon;
            return (
              <div
                key={message.id}
                className={`p-6 hover:bg-white/10 transition-colors ${message.unread ? 'bg-white/10' : ''}`}
              >
                <div className="flex items-start gap-4">
                  <div className={`p-3 rounded-xl  ${message.bgColor}`}>
                    <IconComponent size={24} className={message.color} />
                  </div>
                  <div className="flex-1"> 
                    <div className="flex justify-between items-start mb-1">
                      <h3 className="font-medium text-white">
                        {message.title}
                        {message.unread && (
                          <span className="ml-2 inline-block px-2 py-0.5 text-xs bg-yellow-400 text-black rounded-full">
                            New
                          </span>
                        )}
                      </h3>
                    </div>
                    <p className="text-sm text-white/60 mb-2">{message.message}</p>
                    <p className="text-xs text-white/40">{message.time}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {MESSAGES.length === 0 && (
          <div className="p-8 text-center text-white/60">
            <MessageCircle size={40} className="mx-auto mb-4 opacity-40" />
            <p>No messages yet</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Messages;