import React, { useState, useEffect } from 'react';
import { ChevronLeft, Edit2, Star, BanknoteIcon, X, Share2, <PERSON><PERSON>, ExternalLink, CheckCircle } from 'lucide-react';
import { useAuth } from '../auth/AuthContext';
import useFetch from '../hooks/useFetch';
import { GET_USER_COINS, GET_USER_PROFILE } from '../api/auth';
import ProfileSettings from './ProfileSettings';
import { CustomToast } from '../utils/validations/customeToast';
import Whatsaap from '../assets/images/wapp.svg';
import TeliGram from '../assets/images/Artboard.svg';
import email from '../assets/images/email.svg';
import { IMAGES } from '../constant/image';
import { useSocketContext } from '../context/socketProvider';

interface ProfileProps {
  onClose: () => void;
  balance: {
    coins: number;
    fpp: number;
    level: number;
    xp: number;
    real_money: string;
  };
}

/**
 * Formats a number or numeric string into a localized string with commas.
 */
const formatCoins = (value: number | string): string =>
  Number(value).toLocaleString();

/**
 * Gets the level-specific image based on user level (0-12)
 */
const getLevelImage = (level: number): string => {
  // Map levels to corresponding MAP images
  const levelImageMap: { [key: number]: string } = {

    0: IMAGES.MAP_12,   // Level 0 -> MAP_1
    1: IMAGES.MAP_1,   // Level 1 -> MAP_1 (SHOT CALLER)
    2: IMAGES.MAP_2,   // Level 2 -> MAP_2 (CORNER HUSTLER)
    3: IMAGES.MAP_3,   // Level 3 -> MAP_3 (BORDER RUNNER)
    4: IMAGES.MAP_4,   // Level 4 -> MAP_4 (STREET BOSS)
    5: IMAGES.MAP_5,   // Level 5 -> MAP_5 (UNDERBOSS)
    6: IMAGES.MAP_6,   // Level 6 -> MAP_6 (OG)
    7: IMAGES.MAP_7,   // Level 7 -> MAP_7 (CONNECTOR)
    8: IMAGES.MAP_8,   // Level 8 -> MAP_8 (KINGPIN)
    9: IMAGES.MAP_9,   // Level 9 -> MAP_9 (STREET SCOUT)
    10: IMAGES.MAP_10, // Level 10 -> MAP_10 (LEGENDARY TOPDOG)
    11: IMAGES.MAP_11, // Level 11 -> MAP_11 (CAPO)
    12: IMAGES.MAP_11  // Level 12 -> MAP_11 (max level)
  };

  // Return the level-specific image or default to MAP_1 for invalid levels
  return levelImageMap[level] || IMAGES.MAP_1;
};

/**
 * Gets the level title based on user level (0-12)
 */
const getLevelTitle = (level: number): string => {
  const levelTitleMap: { [key: number]: string } = {
    0: 'SHOT CALLER',
    1: 'SHOT CALLER',
    2: 'CORNER HUSTLER',
    3: 'BORDER RUNNER',
    4: 'STREET BOSS',
    5: 'UNDERBOSS',
    6: 'OG',
    7: 'CONNECTOR',
    8: 'KINGPIN',
    9: 'STREET SCOUT',
    10: 'LEGENDARY TOPDOG',
    11: 'CAPO',
  };

  return levelTitleMap[level] || 'NEWCOMER';
};

const SHARE_OPTIONS = [
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    icon: Whatsaap,
    color: 'text-green-400',
    bgColor: 'bg-green-400/20'
  },
  {
    id: 'telegram',
    name: 'Telegram',
    icon: TeliGram,
    color: 'text-blue-400',
    bgColor: 'bg-blue-400/20'
  },
  {
    id: 'email',
    name: 'Email',
    icon: email,
    color: 'text-purple-400',
    bgColor: 'bg-purple-400/20'
  }
];

const Profile: React.FC<ProfileProps> = ({ onClose, balance }) => {
  const { accessToken } = useAuth();
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [copied, setCopied] = useState(false);

    const {
      levelPlayerState,
      connectLevelPlayerSocket,
    } = useSocketContext();
  // Fetch user profile
  const { data: userProfile } = useFetch(GET_USER_PROFILE, {
    headers: { Authorization: `Bearer ${accessToken}` },
  });

  // Fetch user coins
  const { data } = useFetch(GET_USER_COINS, {
    headers: { Authorization: `Bearer ${accessToken}` },
  });
  const points = data?.data?.point;



  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Trigger animation
    setTimeout(() => setIsAnimating(true), 50);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Share referral link or copy to clipboard fallback
  const referralCode = userProfile?.referral_code;
  const shareUrl = referralCode ? `${window.location.origin}/auth?ref=${referralCode}` : '';

  const handleCopyLink = async () => {
    if (!shareUrl) return;
    await navigator.clipboard.writeText(shareUrl);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
    CustomToast('success', 'Referral link copied!');
  };

  const handleShare = (platform: string) => {
    if (!shareUrl) return;
    let shareLink = '';
    const message = `Join me on Rise & Hustle! Use my referral link: ${shareUrl}`;
    switch (platform) {
      case 'whatsapp':
        shareLink = `https://wa.me/?text=${encodeURIComponent(message)}`;
        break;
      case 'telegram':
        shareLink = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent('Join me on Rise & Hustle!')}`;
        break;
      case 'email':
        shareLink = `mailto:?subject=Join me on Rise & Hustle!&body=${encodeURIComponent(message)}`;
        break;
    }
    window.open(shareLink, '_blank');
  };

  // Mobile bottom sheet styles
  const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const mobileContentClass = `
  bg-gradient-to-b from-[#510957] to-black
    rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out
    ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;

  // Desktop modal styles
  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const desktopContentClass = `
   bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
  rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col
  transform transition-all duration-300 ease-out
  ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
`;

  return (
    <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
      <div className={isMobile ? mobileContentClass : desktopContentClass}>
        {/* Header */}
        <div className="p-6  flex justify-between items-center sticky top-0  from-[#510957] to-[#510957]rounded-t-3xl md:rounded-t-2xl z-10">
          {/* <button onClick={onClose} className="text-white/60 hover:text-white">
            <ChevronLeft size={24} />
          </button> */}
          <div className="flex-1 ">
            <h2 className="text-xl  text-white font-[Anton] tracking-wide">Profile</h2>
          </div>
          <button onClick={onClose} className="text-white/60 hover:text-white">
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Profile Header */}
          <div className="flex items-center gap-4">
            <img
              src={
                // userProfile?.profile_picture ||
                getLevelImage(levelPlayerState?.level)
              }
              alt="Profile"
              className="w-20 h-20 rounded-full border-2 border-purple-400"
            />
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-bold">
                    Player ID: {userProfile?.user_id || 'Username'}
                  </h3>
                  <p className="text-white/60">
                    {userProfile?.email || 'Email'}
                  </p>
                </div>
                <button
                  onClick={() => setShowEditProfile(true)}
                  className="bg-white/10 hover:bg-white/20 p-2 rounded-lg transition-colors"
                >
                  <Edit2 size={20} className="text-white-400" />
                </button>
              </div>
              <div className="flex items-center gap-2 mt-2">
                <Star size={16} className="text-yellow-400" />
                <div className="flex flex-col">
                  <span className="font-medium">Level {levelPlayerState?.level}</span>
                  <span className="text-xs text-white/60">{getLevelTitle(levelPlayerState?.level)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Balance Section */}
          <div className="bg-white/10 rounded-xl p-6">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-[#1c1c1c] p-3 rounded-lg">
                <BanknoteIcon
                  size={16}
                  className="text-accent-gold w-4 h-4 sm:w-5 sm:h-5"
                />
              </div>
              <div>
                <p className="text-sm text-white font-[Anton]">Bucks</p>
                <p className="text-2xl text-white ">
                  {formatCoins(Number(points) > 0 ? Number(points).toFixed(0) : "0")}
                </p>

              </div>
            </div>

          </div>

          {/* Referral Section */}
          <div className="bg-white/5 rounded-xl p-6">
            <h4 className="text-lg font-semibold mb-2 font-[Anton] tracking-wide">Referral Code</h4>
            <div className="flex items-center gap-2 mb-2">
              <p className="text-2xl font-bold">
                {userProfile?.referral_code || '---'}
              </p>
              <button
                onClick={() => setShowShareModal(true)}
                className="transition-colors p-2 rounded-lg"
                title="Share referral link"
              >
                <Share2 size={20} className="text-[#ED0CFF]" />
              </button>
            </div>
            <p className="text-white/60">
              Earn 10 Bucks every time someone joins through you
            </p>
          </div>

        </div>
      </div>

      {/* Share Modal */}
      {showShareModal && (
        <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
          <div className={isMobile ? mobileContentClass : desktopContentClass}>
            <div className="p-6 flex justify-between items-center sticky top-0 rounded-t-3xl z-10">
              <h2 className="text-xl text-white font-[Anton] tracking-wide">Share Referral Link</h2>
              <button
                onClick={() => setShowShareModal(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>
            <div className="p-6">
              <div className="bg-white/5 rounded-lg p-4 mb-6 flex items-center justify-between">
                <div className="flex items-center gap-2 flex-1 mr-4">
                  <ExternalLink size={20} className="text-white/60" />
                  <input
                    type="text"
                    value={shareUrl}
                    readOnly
                    className="bg-transparent flex-1 focus:outline-none text-white"
                  />
                </div>
                <button
                  onClick={handleCopyLink}
                  className="bg-white/10 hover:bg-white/20 transition-colors p-2 rounded-lg relative group"
                >
                  {copied ? (
                    <CheckCircle size={20} className="text-green-400" />
                  ) : (
                    <Copy size={20} className="text-white/60 group-hover:text-white" />
                  )}
                </button>
              </div>
              <div className="space-y-4">
                {SHARE_OPTIONS.map(option => (
                  <button
                    key={option.id}
                    onClick={() => handleShare(option.id)}
                    className="w-full bg-white/5 hover:bg-white/10 transition-colors rounded-lg p-4 flex items-center gap-4"
                  >
                    <div className={`p-2 rounded-lg ${option.bgColor}`}>
                      <img src={option.icon} alt="icon" className="h-6 w-6 object-contain" />
                    </div>
                    <span className="font-medium font-[Anton] tracking-wide">Share via {option.name}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {showEditProfile && (
        <ProfileSettings onClose={() => setShowEditProfile(false)} />
      )}
    </div>
  );
};

export default Profile;
