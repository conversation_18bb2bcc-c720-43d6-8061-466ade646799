import React from 'react';
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { toast } from 'react-toastify';
import { CheckCircle, ChevronLeft } from 'lucide-react';
import { resetPassword } from '../api/authService';

interface ResetPasswordProps {
  token: string;
  onSwitchTab: () => void;
}

interface FormValues {
  newPassword: string;
  confirmPassword: string;
}

// ✅ Password strength indicator
const PasswordStrengthIndicator: React.FC<{ password: string; touched?: boolean }> = ({
  password,
  touched
}) => {
  if (!touched || !password) return null;

  const checks = [
    { test: password.length >= 8, label: "8+ chars" },
    { test: /[a-z]/.test(password), label: "lowercase" },
    { test: /[A-Z]/.test(password), label: "uppercase" },
    { test: /\d/.test(password), label: "number" },
    { test: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password), label: "special" },
  ];

  const passedChecks = checks.filter(check => check.test).length;
  const strength = passedChecks <= 2 ? 'weak' : passedChecks <= 4 ? 'medium' : 'strong';

  const strengthColors = {
    weak: 'bg-red-500',
    medium: 'bg-yellow-500',
    strong: 'bg-green-500'
  };

  const strengthTextColors = {
    weak: 'text-red-400',
    medium: 'text-yellow-400',
    strong: 'text-green-400'
  };

  return (
    <div className="mt-2">
      <div className="flex items-center gap-2 mb-2">
        <span className="text-xs text-white/60">Strength:</span>
        <div className="flex-1 bg-white/10 rounded-full h-1.5">
          <div
            className={`h-full rounded-full transition-all duration-300 ${strengthColors[strength]}`}
            style={{ width: `${(passedChecks / 5) * 100}%` }}
          />
        </div>
        <span className={`text-xs font-medium ${strengthTextColors[strength]}`}>
          {strength.charAt(0).toUpperCase() + strength.slice(1)}
        </span>
      </div>

      <div className="flex flex-wrap gap-1">
        {checks.map((check, index) => (
          <span
            key={index}
            className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs ${
              check.test ? 'bg-green-500/20 text-green-400' : 'bg-white/10 text-white/50'
            } transition-colors`}
          >
            {check.test ? <CheckCircle size={10} /> : <div className="w-2 h-2 rounded-full border border-current opacity-50" />}
            {check.label}
          </span>
        ))}
      </div>
    </div>
  );
};

const ResetPasswordSchema = Yup.object().shape({
  newPassword: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .required('New Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('newPassword')], 'Passwords must match')
    .required('Confirm Password is required'),
});

const ResetPassword: React.FC<ResetPasswordProps> = ({ token, onSwitchTab }) => {
  const initialValues: FormValues = { newPassword: '', confirmPassword: '' };

  const handleSubmit = async (
    values: FormValues,
    { setSubmitting, setStatus }: FormikHelpers<FormValues>
  ) => {
    setStatus(undefined);
    try {
      await resetPassword({
        token,
        newPassword: values.newPassword,
        confirmPassword: values.confirmPassword,
      });
      toast.success('Password reset successfully!');
      onSwitchTab();
    } catch (err) {
      setStatus(err instanceof Error ? err.message : 'Failed to reset password.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="max-w-sm mx-auto">
      <div className="flex items-center gap-1.3 mb-4">
        <button type="button" onClick={onSwitchTab} className="text-white">
          <ChevronLeft className="font-bold" />
        </button>
        <h2 className="text-[16px] leading-[24px] font-bold tracking-[0.02rem] uppercase font-[Oswald] text-white">
          RESET PASSWORD
        </h2>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={ResetPasswordSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, status, values, touched }) => (
          <Form>
            {status && <div className="text-red-500 text-sm mb-4">{status}</div>}

            {/* New Password Field */}
            <div className="mb-4">
              <label
                htmlFor="newPassword"
                className="block text-sm font-[400] font-[Poppins] text-white mb-1"
              >
                New Password
              </label>
              <Field
                id="newPassword"
                name="newPassword"
                type="password"
                placeholder="At least 8 characters"
                className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
              />
              <ErrorMessage
                name="newPassword"
                component="div"
                className="text-red-500 text-sm mt-1"
              />

              {/* ✅ Show password strength below password field */}
              <PasswordStrengthIndicator
                password={values.newPassword}
                touched={touched.newPassword}
              />
            </div>

            {/* Confirm Password */}
            <div className="mb-6">
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-[400] font-[Poppins] text-white mb-1"
              >
                Confirm Password
              </label>
              <Field
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                placeholder="Re-enter new password"
                className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
              />
              <ErrorMessage
                name="confirmPassword"
                component="div"
                className="text-red-500 text-sm mt-1"
              />
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-[#ED0CFF] hover:bg-[#d30ae0] tracking-wider
                     font-bold uppercase font-[Anton] transition duration-200 text-white font-normal py-2 rounded-lg"
            >
              {isSubmitting ? 'Resetting…' : 'Reset Password'}
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default ResetPassword;
