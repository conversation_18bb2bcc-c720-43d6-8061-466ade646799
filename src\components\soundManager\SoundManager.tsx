import React, { useRef, useEffect } from 'react';

export type SoundKey = 'background' | 'win';

export interface SoundManagerProps {
  /** Map of sound keys to imported URLs */
  sounds: Record<SoundKey, string>;
  /** Current active background key to loop (or null to stop) */
  backgroundKey?: SoundKey | null;
  /** One-off event sound key to play when this prop changes */
  playKey?: SoundKey;
  /** Volume settings per sound key (0.0 to 1.0) */
  volumes?: Partial<Record<SoundKey, number>>;
}

/**
 * A component to manage background music and a single 'win' effect.
 *
 * Example usage:
 * ```tsx
 * <SoundManager
 *   sounds={{
 *     background: bgUrl,
 *     win:        winUrl,
 *   }}
 *   backgroundKey={isPlaying ? 'background' : null}
 *   playKey={didWin ? 'win' : undefined}
 *   volumes={{ background: 0.5, win: 1.0 }}
 * />
 * ```
 */
const SoundManager: React.FC<SoundManagerProps> = ({ sounds, backgroundKey = null, playKey, volumes = {} }) => {
  const audioRefs = useRef<Record<SoundKey, HTMLAudioElement>>({} as any);

  useEffect(() => {
    Object.entries(sounds).forEach(([key, url]) => {
      const audio = new Audio(url);
      // loop only for background
      audio.loop = (key === 'background');
      audio.volume = volumes[key as SoundKey] ?? 1;
      audioRefs.current[key as SoundKey] = audio;
    });

    return () => {
      Object.values(audioRefs.current).forEach(audio => audio.pause());
    };
  }, []);

  // Handle background loop start/stop
  useEffect(() => {
    (['background'] as SoundKey[]).forEach(key => {
      const audio = audioRefs.current[key];
      if (key === backgroundKey && backgroundKey) {
        audio.currentTime = 0;
        audio.play().catch(() => {});
      } else {
        audio.pause();
      }
    });
  }, [backgroundKey]);

  // Handle one-off win sound
  useEffect(() => {
    if (playKey === 'win') {
      const audio = audioRefs.current.win;
      audio.currentTime = 0;
      audio.play().catch(() => {});
    }
  }, [playKey]);

  // Adjust volumes on change
  useEffect(() => {
    Object.entries(volumes).forEach(([key, vol]) => {
      const audio = audioRefs.current[key as SoundKey];
      if (audio) audio.volume = vol!;
    });
  }, [volumes]);

  return null;
};

export default SoundManager;
