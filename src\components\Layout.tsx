import React, { useState, useRef, useEffect } from 'react';
import LoginPromptPopup from './LoginPromptPopup';
// --- BEGIN: Level Up Modal Support ---
// Copy of markers from Map.tsx (should ideally be moved to a shared file)
import { IMAGES } from '../constant/image';

const markers = [
  { x: 1300, y: 690, avatar: IMAGES.MAP_12, title: 'SHOT CALLER', price: '$100.00', bg_image: IMAGES.SHOTE_CELLER_2, bg_color: '#31ad09', share_title: "You Just Levelled Up. Keep Climbing", share_description: "You've just earned 12,000 Bucks & 5,000 Rise Tokens ", clickable: false },
  { x: 1100, y: 620, avatar: IMAGES.MAP_1, title: 'SHOT CALLER', price: '$100.00', bg_image: IMAGES.SHOTE_CELLER_2, bg_color: '#31ad09', share_title: "You Just Levelled Up. Keep Climbing", share_description: "You've just earned 12,000 Bucks & 5,000 Rise Tokens ", clickable: true },
  { x: 1250, y: 150, avatar: IMAGES.MAP_2, title: 'CORNER HUSTLER', price: '$100.00', bg_image: IMAGES.CORNER_HUSTLE, bg_color: '#31ad09', share_title: "The Streets Are Watching. You're Rising", share_description: "You've just earned 1,000 Bucks & 100 Rise Tokens ", clickable: true },
  { x: 460, y: 360, avatar: IMAGES.MAP_3, title: 'BORDER RUNNER', price: '$100.00', bg_image: IMAGES.SHOT_CALLER, bg_color: '#6309b1', share_title: "Next Rank Unlocked:The Streets Respect You", share_description: "You've just earned 7,000 Bucks & 1,000 Rise Tokens ", clickable: true },
  { x: 480, y: 1100, avatar: IMAGES.MAP_4, title: 'STREET BOSS', price: '$100.00', bg_image: IMAGES.STREET_BOSS, bg_color: '#0888ad', share_title: "Another Step Closer to Legendary Topdog", share_description: "You've just earned 15,000 Bucks & 7,000 Rise Tokens ", clickable: true },
  { x: 300, y: 1400, avatar: IMAGES.MAP_5, title: 'UNDERBOSS', price: '$100.00', bg_image: IMAGES.UNDERBOSS, bg_color: '#b08908', share_title: "Rank Up! Your Legend Grows.", share_description: "You've just earned 25,000 Bucks & 15,000 Rise Tokens ", clickable: true },
  { x: 1080, y: 1600, avatar: IMAGES.MAP_6, title: 'OG', price: '$100.00', bg_image: IMAGES.OG, bg_color: '#33b009', share_title: "Power Moves Only. You're Rising", share_description: "You've just earned 30,000 Bucks & 20,000 Rise Tokens ", clickable: true },
  { x: 830, y: 970, avatar: IMAGES.MAP_7, title: 'CONNECTOR', price: '$100.00', bg_image: IMAGES.CONNECTOR, bg_color: '#b08908', share_title: "Respect Earned. Power Gained.", share_description: "You've just earned 10,000Buck's & 2,000 Rise Tokens ", clickable: true },
  { x: 700, y: 1730, avatar: IMAGES.MAP_8, title: 'KINGPIN', price: '$100.00', bg_image: IMAGES.KINGPIN, bg_color: '#088baf', share_title: "Standing On The Edge Of Legends", share_description: "You've just earned 40,000 Bucks & 50,000 Rise Tokens ", clickable: true },
  { x: 840, y: 150, avatar: IMAGES.MAP_9, title: 'STREET SCOUT', price: '$100.00', bg_image: IMAGES.STREET_SCOUT, bg_color: '#0888ad', share_title: "Level Up: Your Hustle Just Got Stronger", share_description: "You've just earned 5,000 Bucks & 500 Rise Tokens ", clickable: true },
  { x: 1150, y: 2300, avatar: IMAGES.MAP_10, title: 'LEGENDARY TOPDOG', price: '$100.00', bg_image: IMAGES.SHOT_CALLER_3, bg_color: '#6309b1', share_title: "The Kingdom Is Yours. Rule With Pride, Glory & Respect", share_description: "You've just earned 100,000 Rise Tokens", clickable: true },
  { x: 1480, y: 1300, avatar: IMAGES.MAP_11, title: 'CAPO', price: '$100.00', bg_image: IMAGES.CAPO, bg_color: '#6309b1', share_title: "Hustle Upgraded. New Rewards Await", share_description: "You've just earned 20,000 Bucks & 10,000 Rise Tokens ", clickable: true },
];
// --- END: Level Up Modal Support ---

import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import {
  BanknoteIcon,
  PlayCircle,
  Award,
  Menu,
  Bell,
  LogOut,
  HelpCircle,
  Settings as SettingsIcon,
  Shield,
  FileText,
  Zap,
  User,
  Users,
  Map,
  Star,
  Receipt,
  X,
} from 'lucide-react';
import Messages from './Messages';
import TermsAndConditions from './TermsAndConditions';
import PrivacyPolicy from './PrivacyPolicy';
import Settings from './Settings';
import HelpSupport from './HelpSupport';
import VideoAd from './VideoAd';
import BoostModal from './BoostModal';
import Profile from './Profile';
import Transactions from './Transactions';
import { useAuth } from '../auth/AuthContext';
import useFetch from '../hooks/useFetch';
import { GET_USER_PROFILE } from '../api/auth';
import { useCoins } from '../hooks/useCoinsQuery';
import ZendeskWidget from './ZendeskWidget';
import { useSocketContext } from '../context/socketProvider';

interface LayoutProps {
  balance: {
    coins: number;
    fpp: number;
    level: number;
    xp: number;
    real_money: string;
    bucks_wagered: number;
    bucks_required: number;
  };
}

/**
 * Formats a number or numeric string into a localized string with commas.
 * @param {number | string} value - The value to format.
 * @returns {string} Formatted number string.
 */
const formatCoins = (value: number | string): string =>
  Number(value).toLocaleString();

const Layout: React.FC<LayoutProps> = ({ balance }) => {
  const { isAuthenticated } = useAuth();
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  
  const handleRequireLogin = (e?: React.MouseEvent<HTMLButtonElement>) => {
    if (e) e.preventDefault();
    setShowLoginPrompt(true);
  };

  // Level up modal state
  const [showLevelUp, setShowLevelUp] = useState(false);
  const [levelUpMarker, setLevelUpMarker] = useState<any>(null);
  const prevLevelRef = useRef<number | null>(null);

  const { levelPlayerState, connectLevelPlayerSocket } = useSocketContext();

  const [menuOpen, setMenuOpen] = useState(false);
  const [showTerms, setShowTerms] = useState(false);
  const [showPrivacy, setShowPrivacy] = useState(false);
  const [showMessages, setShowMessages] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [showVideoAd, setShowVideoAd] = useState(false);
  const [showBoostModal, setShowBoostModal] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showTransactions, setShowTransactions] = useState(false);
  const [showZendesk, setShowZendesk] = useState(false);

  const location = useLocation();
  const navigate = useNavigate();
  const menuRef = useRef<HTMLDivElement>(null);
  const { logout, accessToken } = useAuth();
  const { coins } = useCoins();

  const { data: userProfile, loading, error } = useFetch(GET_USER_PROFILE, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  useEffect(() => {
    connectLevelPlayerSocket();
    setShowLevelUp(false);
  }, []);

  // Only show the level‑up modal when the level increases:
  useEffect(() => {
    const newLevel = levelPlayerState?.level;
    const prevLevel = prevLevelRef.current;
    if (newLevel !== undefined && prevLevel !== null && newLevel > prevLevel) {
      // set the correct marker for the new level
      setLevelUpMarker(markers[newLevel]);
      setShowLevelUp(true);
    }
    // always update ref for next change
    if (newLevel != null) {
      prevLevelRef.current = newLevel;
    }
  }, [levelPlayerState?.level]);

  useEffect(() => {
    // 1️⃣ Prevent closing the menu when clicking outside:
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    };

    // 2️⃣ Show Zendesk (so it mounts the launcher)
    setShowZendesk(true);

    // 3️⃣ Inject CSS override for mobile:
    const styleTag = document.createElement('style');
    styleTag.innerHTML = `
      @media (max-width: 768px) {
        /* Zendesk's default launcher has id="launcher" */
        #launcher {
          bottom: 4.5rem !important;  /* Push it above the 56px nav */
          right: 1rem !important;    /* Slight inset from the right */
          z-index: 9999 !important;  /* Keep it on top */
        }
      }
    `;
    document.head.appendChild(styleTag);

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.head.removeChild(styleTag);
    };
  }, []);

  const handleShare = (platform: string) => {
    console.log(`Sharing to ${platform}`);
  };

  return (
    <div className="min-h-screen bg-black text-text-primary">
      {/* Level Up Modal */}
      {showLevelUp && levelUpMarker && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
          <div className="bg-[#181818] rounded-2xl shadow-2xl p-6 max-w-xs w-full flex flex-col items-center relative">
            <button
              className="absolute top-3 right-3 text-white"
              onClick={() => setShowLevelUp(false)}
            >
              <X size={24} />
            </button>
            <img
              src={levelUpMarker.avatar}
              alt={levelUpMarker.title}
              className="w-20 h-20 rounded-full mb-4"
            />
            <h2 className="text-xl font-bold mb-2 text-center">
              {levelUpMarker.title}
            </h2>
            <p className="text-sm text-white/80 mb-2 text-center">
              {levelUpMarker.share_title}
            </p>
            <p className="text-xs text-white/60 mb-4 text-center">
              {levelUpMarker.share_description}
            </p>
            <button
              className="w-full bg-primary text-white py-2 rounded-xl font-bold mt-2"
              onClick={() => {
                setShowLevelUp(false);
                navigate('/map');
              }}
            >
              Go to Map
            </button>
          </div>
        </div>
      )}

      {/* Login Prompt Popup for unauthenticated actions */}
      {!isAuthenticated && (
        <LoginPromptPopup isOpen={showLoginPrompt} onClose={() => setShowLoginPrompt(false)} />
      )}

      {/* Header */}
      {!isAuthenticated ? (
        <header className="sticky top-0 bg-black-card/80 bg-black backdrop-blur-sm p-4 flex justify-between items-center z-50">
          <img src={require('../../logo-white.avif')} alt="Logo" className="h-8 w-auto" />
          <div className="flex gap-2">
            <button 
              className="bg-[#ED0CFF] text-white px-4 py-2 rounded-lg font-semibold hover:bg-[#ED0CFF]/80 transition-colors" 
              onClick={handleRequireLogin}
            >
              Sign Up
            </button>
            <button 
              className="border border-white/20 text-white px-4 py-2 rounded-lg font-semibold hover:bg-white/10 transition-colors" 
              onClick={handleRequireLogin}
            >
              Login
            </button>
          </div>
        </header>
      ) : (
        <header className="sticky top-0 bg-black-card/80 bg-black backdrop-blur-sm p-4 flex justify-center items-center z-50">
          <div className="flex items-center space-x-2 sm:space-x-4 md:space-x-6">
            {/* Greeting */}
            <p className="font-[haydes] font-bold text-sm sm:text-base md:text-lg">
              Hii {userProfile?.first_name}
            </p>

            {/* Level Badge */}
            <div className="ml-auto w-32 sm:w-40 md:w-52 bg-[#1c1c1c] rounded-md p-[12px]">
              {/* Bucks Progress Display */}
              <div className="flex flex-col gap-1">
                {/* Progress Bar Row with Star on left */}
                <div className="flex items-center gap-2">
                  {/* Star and Level (left side) */}
                  <div className="flex items-center mr-2">
                    <Star size={16} className="text-accent-gold mr-1" />
                    <span className="text-xs text-white">
                      {levelPlayerState?.level || '0'}
                    </span>
                  </div>
                  {/* Progress Bar Container */}
                  <div className="w-full bg-gray-700 rounded-full h-2.5 overflow-hidden">
                    <div
                      className="bg-primary h-2.5 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min(
                          (levelPlayerState?.amount_spent_to_reach_level ?? 0) /
                          (levelPlayerState?.next_level_requirement ?? 1) * 100,
                          100
                        ).toFixed(2)}%`,
                      }}
                    />
                  </div>
                  {/* Bucks Wagered vs Required (right side) */}
                  <span className="text-xs text-white font-semibold ml-2 whitespace-nowrap">
                    {formatCoins(levelPlayerState?.amount_spent_to_reach_level ?? 0)} / {formatCoins(levelPlayerState?.next_level_requirement ?? 0)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-1.5 sm:gap-2 md:gap-3 sm:px-[3px] relative" ref={menuRef}>
            <div className="ml-auto flex items-center gap-1.5 sm:gap-2 md:gap-3 px-4 sm:px-3 md:px-4 py-1.5 sm:py-2 rounded-lg bg-[#1c1c1c]">
              <BanknoteIcon size={16} className="text-accent-gold w-4 h-4 sm:w-5 sm:h-5" />
              <span className="text-xs sm:text-sm md:text-base font-normal text-white">
                {formatCoins(balance.coins || 0)} Bucks
              </span>
            </div>

            <button
              onClick={() => setShowMessages(true)}
              className="relative hover:text-primary transition-colors"
            >
              <Bell size={23} className="fill-current text-[#9c9c9c] stroke-none" />
              <span className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full" />
            </button>

            <button
              onClick={() => setMenuOpen(!menuOpen)}
              className="hover:text-primary transition-colors"
            >
              {!menuOpen ? <Menu size={24} /> : <X size={24} />}
            </button>

            {menuOpen && (
              <div className="absolute right-0 top-[3.5rem] w-64 bg-surface-card rounded-xl shadow-lg border border-text-muted/10 overflow-hidden z-50">
                {/* User Info */}
                <div className="p-4 border-b border-text-muted/10">
                  <div className="flex items-center gap-3">
                    <div>
                      <h3 className="font-semibold text-xs">
                        Player ID: {userProfile?.user_id}
                      </h3>
                      <p className="text-sm text-text-muted">
                        {userProfile?.email}
                      </p>
                    </div>
                  </div>
                </div>
                {/* Menu Items */}
                <div className="p-2">
                  <button
                    onClick={() => {
                      setMenuOpen(false);
                      setShowProfile(true);
                    }}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <User size={18} className="text-primary" />
                    <span>Profile</span>
                  </button>
                  <button
                    onClick={() => {
                      setMenuOpen(false);
                      setShowTransactions(true);
                    }}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <Receipt size={18} className="text-primary" />
                    <span>Transactions</span>
                  </button>
                  <button
                    onClick={() => {
                      setMenuOpen(false);
                      setShowSettings(true);
                    }}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <SettingsIcon size={18} className="text-primary" />
                    <span>Settings</span>
                  </button>
                  <button
                    onClick={() => {
                      setMenuOpen(false);
                      setShowHelp(true);
                    }}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <HelpCircle size={18} className="text-primary" />
                    <span>Help & Support</span>
                  </button>
                  <Link
                    to="/terms-and-conditions"
                    onClick={() => setMenuOpen(false)}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <FileText size={18} className="text-primary" />
                    <span>Terms & Conditions</span>
                  </Link>
                  <Link
                    to="/privacy-policy"
                    onClick={() => setMenuOpen(false)}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left"
                  >
                    <Shield size={18} className="text-primary" />
                    <span>Privacy Policy</span>
                  </Link>
                  <div className="h-px bg-text-muted/10 my-2" />
                  <button
                    onClick={logout}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-text-muted/10 transition-colors text-left text-error"
                  >
                    <LogOut size={18} />
                    <span>Sign Out</span>
                  </button>
                </div>
                {/* Version */}
                <div className="p-3 text-center border-t border-text-muted/10">
                  <p className="text-xs text-text-muted">Version 1.0.0</p>
                </div>
              </div>
            )}
          </div>
        </header>
      )}

      {/* Main Content */}
      <main className="p-4 max-w-md mx-auto pb-24">
        <Outlet />
      </main>

      {/* Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-[#131313E0] backdrop-blur-sm p-4 pt-0 border-t border-text-muted/10 z-30">
        <div className="max-w-md mx-auto flex justify-around">
          {!isAuthenticated ? (
            <>
              <button onClick={handleRequireLogin} className="flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors">
                <PlayCircle size={24} />
                <span className="text-xs">Hustle</span>
              </button>
              <button onClick={handleRequireLogin} className="flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors">
                <BanknoteIcon size={24} />
                <span className="text-xs">Wallet</span>
              </button>
              <button onClick={handleRequireLogin} className="flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors">
                <Users size={24} />
                <span className="text-xs">Squads</span>
              </button>
              <button onClick={handleRequireLogin} className="flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors">
                <Map size={24} />
                <span className="text-xs">Map</span>
              </button>
              <button onClick={handleRequireLogin} className="flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors">
                <Zap size={24} />
                <span className="text-xs">Boost</span>
              </button>
            </>
          ) : (
            <>
              <Link
                to="/"
                className={`flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors ${
                  location.pathname === '/'
                    ? 'text-[#ED0CFF] border-t-[2px] border-[#ED0CFF]'
                    : ''
                }`}
              >
                <PlayCircle size={24} />
                <span className="text-xs">Hustle</span>
              </Link>
              <Link
                to="/wallet"
                className={`flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors ${
                  location.pathname === '/wallet'
                    ? 'text-[#ED0CFF] border-t-[2px] border-[#ED0CFF]'
                    : ''
                }`}
              >
                <BanknoteIcon size={24} />
                <span className="text-xs">Wallet</span>
              </Link>
              <Link
                to="/squads"
                className={`flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors ${
                  location.pathname === '/squads'
                    ? 'text-[#ED0CFF] border-t-[2px] border-[#ED0CFF]'
                    : ''
                }`}
              >
                <Users size={24} />
                <span className="text-xs">Squads</span>
              </Link>
              <Link
                to="/map"
                className={`flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors ${
                  location.pathname === '/map'
                    ? 'text-[#ED0CFF] border-t-[2px] border-[#ED0CFF]'
                    : ''
                }`}
              >
                <Map size={24} />
                <span className="text-xs">Map</span>
              </Link>
              <button
                onClick={() => setShowBoostModal(true)}
                className="flex flex-col items-center gap-1 hover:text-[#ED0CFF] pt-4 transition-colors"
              >
                <Zap size={24} />
                <span className="text-xs">Boost</span>
              </button>
            </>
          )}
        </div>

        {/* Only render ZendeskWidget after mounting */}
        {showZendesk && (
          <ZendeskWidget userProfileState={{ userProfile, loading, error }} />
        )}
      </nav>

      {/* Modals */}
      {showMessages && <Messages onClose={() => setShowMessages(false)} />}
      {showTerms && <TermsAndConditions />}
      {showPrivacy && <PrivacyPolicy />}
      {showSettings && <Settings onClose={() => setShowSettings(false)} />}
      {showHelp && <HelpSupport onClose={() => setShowHelp(false)} />}
      {showProfile && (
        <Profile onClose={() => setShowProfile(false)} balance={balance} />
      )}
      {showTransactions && (
        <Transactions
          onClose={() => setShowTransactions(false)}
          balance={balance}
        />
      )}
      {showVideoAd && (
        <VideoAd
          onClose={() => setShowVideoAd(false)}
          onComplete={() => {
            // Handle reward
          }}
        />
      )}
      {showBoostModal && (
        <BoostModal
          onClose={() => setShowBoostModal(false)}
          onWatchAd={() => {
            setShowBoostModal(false);
            setShowVideoAd(true);
          }}
          onShare={handleShare}
        />
      )}
    </div>
  );
};

export default Layout;