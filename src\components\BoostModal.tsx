import React ,{useState,useEffect}from 'react';
import { X, Zap, Facebook, Twitter, Instagram } from 'lucide-react';

interface BoostModalProps {
  onClose: () => void;
  onWatchAd: () => void;
  onShare: (platform: string) => void;
}

const BOOST_OPTIONS = [
  {
    id: 'watch_ad',
    title: 'Watch Video',
    description: 'Watch a short video to earn Bucks',
    icon: Zap,
    xp: 50,
  },
  {
    id: 'share_facebook',
    title: 'Share on Facebook',
    description: 'Share your progress with friends',
    icon: Facebook,
    xp: 100,
  },
  {
    id: 'share_twitter',
    title: 'Share on Twitter',
    description: 'Tweet about your achievements',
    icon: Twitter,
    xp: 100,
  },
  {
    id: 'share_instagram',
    title: 'Share on Instagram',
    description: 'Post your game highlights',
    icon: Instagram,
    xp: 100,
  },
];



const BoostModal: React.FC<BoostModalProps> = ({ onClose, onWatchAd, onShare }) => {
    const [isMobile, setIsMobile] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);


      // Check if mobile on mount and resize
      useEffect(() => {
        const checkMobile = () => {
          setIsMobile(window.innerWidth < 768);
        };
    
        checkMobile();
        window.addEventListener('resize', checkMobile);
    
        // Trigger animation
        setTimeout(() => setIsAnimating(true), 50);
    
        return () => window.removeEventListener('resize', checkMobile);
      }, []);
    const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const mobileContentClass = `
  bg-gradient-to-b from-[#510957] to-black
    rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out
    ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;

  // Desktop modal styles
  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const desktopContentClass = `
   bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
  rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col
  transform transition-all duration-300 ease-out
  ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
`;
  return (
    <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
      <div className={isMobile ? mobileContentClass : desktopContentClass}>
        {/* <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl w-full max-w-md"> */}
        <div className="p-6  flex justify-between items-center sticky top-0  from-[#510957] to-[#510957]rounded-t-3xl md:rounded-t-2xl z-10">
            <h2 className="text-[20px] font-bold flex items-center gap-2 font-[Anton]">
              Boost Your Progress
            </h2>
            <button 
              onClick={onClose}
              className="text-white/60 hover:text-white transition-colors"
            >
              <X size={24} />
            </button>
          </div>

          <div className="p-6 space-y-4">
            {BOOST_OPTIONS.map(option => {
              const IconComponent = option.icon;
              return (
                <button
                  key={option.id}
                  onClick={() => {
                    if (option.id === 'watch_ad') {
                      onWatchAd();
                    } else {
                      onShare(option.id);
                    }
                    onClose();
                  }}
                  className="w-full bg-[#131313] p-4 rounded-xl  transition-colors flex items-center gap-4"
                >
                  <div className="bg-white/10 p-3 rounded-lg">
                    <IconComponent size={24} className="text-[#ED0CFF]" />
                  </div>
                  <div className="flex-1 text-left">
                    <h3 className="font-[14px] font-[Anton] font-normal">{option.title}</h3>
                    <p className="text-[12px] text-white/60 w-[129px] h-[32px]">{option.description}</p>
                  </div>
                  <div className="flex items-center gap-1 text-white font-[Anton]">
                    <span>+ {option.xp}</span>
                    <span className="text-[14px] uppercase">Bucks</span>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      {/* </div> */}
    </div>
  );
};

export default BoostModal;