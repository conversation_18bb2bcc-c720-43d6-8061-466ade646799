import React, { useState, useEffect } from 'react';
import { X, Mail, ArrowRight, Shield, CheckCircle, AlertCircle } from 'lucide-react';

interface ChangeEmailProps {
  onClose: () => void;
}

const ChangeEmail: React.FC<ChangeEmailProps> = ({ onClose }) => {
  const [step, setStep] = useState<'current' | 'new' | 'verify' | 'success'>('current');
  const [currentEmail, setCurrentEmail] = useState('<EMAIL>');
  const [newEmail, setNewEmail] = useState('');
  const [password, setPassword] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.querySelector(`input[name="code-${index + 1}"]`) as HTMLInputElement;
        if (nextInput) nextInput.focus();
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      const prevInput = document.querySelector(`input[name="code-${index - 1}"]`) as HTMLInputElement;
      if (prevInput) prevInput.focus();
    }
  };

  const handleCurrentStep = (e: React.FormEvent) => {
    e.preventDefault();
    if (password.length < 8) {
      setError('Please enter your current password');
      return;
    }
    setError(null);
    setStep('new');
  };

  const handleNewEmailStep = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newEmail || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newEmail)) {
      setError('Please enter a valid email address');
      return;
    }
    if (newEmail === currentEmail) {
      setError('New email must be different from current email');
      return;
    }
    setError(null);
    setStep('verify');
  };

  const handleVerification = (e: React.FormEvent) => {
    e.preventDefault();
    const code = verificationCode.join('');
    if (code.length !== 6) {
      setError('Please enter the complete verification code');
      return;
    }
    setError(null);
    setStep('success');
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl w-full max-w-lg flex flex-col">
        <div className="p-6 border-b border-white/10 flex justify-between items-center bg-gradient-to-r from-[#510957] to-[#510957] rounded-t-2xl">
          <h2 className="text-xl text-white font-[Anton] tracking-wide">Change Email</h2>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6 text-white">
          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-8">
            {['current', 'new', 'verify'].map((s, index) => (
              <React.Fragment key={s}>
                <div className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    step === s 
                      ? 'bg-[#ED0CFF] text-white' 
                      : step === 'success' || ['current', 'new', 'verify'].indexOf(step) > ['current', 'new', 'verify'].indexOf(s)
                      ? 'bg-green-500 text-white'
                      : 'bg-white/10 text-white/60'
                  }`}>
                    {step === 'success' || ['current', 'new', 'verify'].indexOf(step) > ['current', 'new', 'verify'].indexOf(s)
                      ? <CheckCircle size={16} />
                      : index + 1
                    }
                  </div>
                  <span className="ml-2 text-sm font-medium capitalize">{s}</span>
                </div>
                {index < 2 && (
                  <div className="flex-1 mx-4 h-0.5 bg-white/10" />
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Current Email Step */}
          {step === 'current' && (
            <form onSubmit={handleCurrentStep} className="space-y-6">
              <div className="bg-white/5 rounded-xl p-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="bg-blue-500/20 p-2 rounded-lg">
                    <Mail size={20} className="text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-white/60">Current Email</p>
                    <p className="font-medium">{currentEmail}</p>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Confirm Password
                </label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-yellow-500"
                  placeholder="Enter your password"
                />
              </div>

              {error && (
                <div className="flex items-center gap-2 text-red-400 text-sm">
                  <AlertCircle size={16} />
                  <span>{error}</span>
                </div>
              )}

              <button
                type="submit"
                className="w-full py-3 bg-[#ED0CFF] text-white rounded-lg font-medium hover:bg-[#d30ae0] transition-colors"
              >
                Continue
              </button>
            </form>
          )}

          {/* New Email Step */}
          {step === 'new' && (
            <form onSubmit={handleNewEmailStep} className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">
                  New Email Address
                </label>
                <input
                  type="email"
                  value={newEmail}
                  onChange={(e) => setNewEmail(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-[#ED0CFF]"
                  placeholder="Enter new email address"
                />
              </div>

              {error && (
                <div className="flex items-center gap-2 text-red-400 text-sm">
                  <AlertCircle size={16} />
                  <span>{error}</span>
                </div>
              )}

              <div className="flex gap-4">
                <button
                  type="button"
                  onClick={() => setStep('current')}
                  className="flex-1 py-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                >
                  Back
                </button>
                <button
                  type="submit"
                  className="flex-1 py-3 bg-[#ED0CFF] text-white rounded-lg font-medium hover:bg-[#d30ae0] transition-colors"
                >
                  Send Verification Code
                </button>
              </div>
            </form>
          )}

          {/* Verification Step */}
          {step === 'verify' && (
            <form onSubmit={handleVerification} className="space-y-6">
              <div className="text-center">
                <div className="bg-yellow-500/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield size={32} className="text-[#ED0CFF]" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Verify Your Email</h3>
                <p className="text-white/60 text-sm">
                  We've sent a verification code to<br />
                  <span className="text-white font-medium">{newEmail}</span>
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-4 text-center">
                  Enter Verification Code
                </label>
                <div className="flex justify-center gap-2">
                  {verificationCode.map((digit, index) => (
                    <input
                      key={index}
                      type="text"
                      name={`code-${index}`}
                      maxLength={1}
                      value={digit}
                      onChange={(e) => handleCodeChange(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(index, e)}
                      className="w-12 h-12 text-center bg-white/10 border border-white/20 rounded-lg text-xl font-mono focus:outline-none focus:border-[#ED0CFF]"
                    />
                  ))}
                </div>
              </div>

              {error && (
                <div className="flex items-center gap-2 text-red-400 text-sm">
                  <AlertCircle size={16} />
                  <span>{error}</span>
                </div>
              )}

              <div className="flex items-center justify-center gap-2 text-sm">
                <ArrowRight size={16} className="text-white/60" />
                <button className="text-[#ED0CFF] hover:text-[#d30ae0]">
                  Resend Code
                </button>
              </div>

              <div className="flex gap-4">
                <button
                  type="button"
                  onClick={() => setStep('new')}
                  className="flex-1 py-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                >
                  Back
                </button>
                <button
                  type="submit"
                  className="flex-1 py-3 bg-[#ED0CFF] text-white rounded-lg font-medium hover:bg-[#d30ae0] transition-colors"
                >
                  Verify
                </button>
              </div>
            </form>
          )}

          {/* Success Step */}
          {step === 'success' && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle size={32} className="text-green-400" />
              </div>
              <h3 className="text-xl font-bold mb-2">Email Changed Successfully!</h3>
              <p className="text-white/60 mb-6">
                Your email has been updated to<br />
                <span className="text-white font-medium">{newEmail}</span>
              </p>
              <button
                onClick={onClose}
                className="w-full py-3 bg-[#ED0CFF] text-white rounded-lg font-medium hover:bg-[#d30ae0] transition-colors"
              >
                Done
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChangeEmail;